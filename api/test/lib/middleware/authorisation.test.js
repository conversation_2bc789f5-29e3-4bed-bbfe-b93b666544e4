const assert = require('assert')
const serviceLocator = require('service-locator')()
const logger = require('mc-logger')
const acl = require('secure/access-control-list')
const createAuthorisationMiddleware = require('../../../lib/middleware/authorisation')
const adminUserWithRootAccess = { roles: ['root'] }
const adminUserWithEditorAccess = { roles: ['editor'] }

function loadAcl(acl, cb) {
  acl.addResource('article', [
    'discover',
    'read',
    'create',
    'update',
    'publish'
  ])
  acl.clearGrants()
  acl.grant('root', 'article', 'discover')
  acl.grant('root', 'article', 'read')
  acl.grant('root', 'article', 'create')
  acl.grant('root', 'article', 'publish')
  acl.grant('editor', 'article', 'discover')
  acl.grant('editor', 'article', 'read')
  acl.grant('editor', 'article', 'create')
  cb(null, acl)
}

describe('Authorisation Middleware', function () {
  var authorisationMiddleware

  before(function () {
    serviceLocator.env = 'development'
    serviceLocator.register('acl', acl(logger))
    serviceLocator.register('logger', logger)
    serviceLocator.register('roleService', { loadAcl: loadAcl })
  })

  describe('Initialisation', function () {
    it('should error if urlRoot or resource is not provided', function () {
      assert.throws(
        function () {
          authorisationMiddleware = createAuthorisationMiddleware(
            serviceLocator
          )
        },
        {
          message: '"urlRoot" and "resource" must be provided'
        }
      )
    })
  })

  describe('Authorisation', function () {
    beforeEach(function (done) {
      done()
    })

    it('should error if authed user is not set', function (done) {
      const req = {}
      const res = {}
      const next = function (error) {
        assert.strictEqual(
          error.message,
          'Expecting an req.authedUser to be set, ensure getAuthedUser middleware is used'
        )
        done()
      }

      authorisationMiddleware = createAuthorisationMiddleware(
        serviceLocator,
        '/articles',
        'article'
      )
      authorisationMiddleware(req, res, next)
    })

    it('should call next if a defaultAction has access', function (done) {
      const req = {
        authedUser: adminUserWithRootAccess,
        method: 'GET',
        route: { path: '/articles' }
      }
      const res = {}
      const next = function (error) {
        assert.strictEqual(error, undefined)
        done()
      }

      authorisationMiddleware = createAuthorisationMiddleware(
        serviceLocator,
        '/articles',
        'article'
      )
      authorisationMiddleware(req, res, next)
    })
    it('should call next if a custom action has access', function (done) {
      const req = {
        authedUser: adminUserWithRootAccess,
        method: 'PUT',
        route: { path: '/articles/:id/publish' }
      }
      const res = {}
      const next = function (error) {
        assert.strictEqual(error, undefined)
        done()
      }
      const customActions = { 'PUT:/articles/:id/publish': 'publish' }

      authorisationMiddleware = createAuthorisationMiddleware(
        serviceLocator,
        '/articles',
        'article',
        customActions
      )
      authorisationMiddleware(req, res, next)
    })

    it('should throw error with unknown route in development', function (done) {
      const req = {
        authedUser: adminUserWithRootAccess,
        method: 'PUT',
        route: { path: '/articles/:id/publish' }
      }
      const res = {}
      const next = function (error) {
        assert.strictEqual(
          error.message,
          'Unknown action for method: PUT path: /articles/:id/publish'
        )
        done()
      }

      serviceLocator.logger.warn = function (message) {
        assert.strictEqual(
          message,
          'Unknown action for method: PUT path: /articles/:id/publish'
        )
        serviceLocator.logger.warn = function () {}
      }

      authorisationMiddleware = createAuthorisationMiddleware(
        serviceLocator,
        '/articles',
        'article'
      )
      authorisationMiddleware(req, res, next)
    })

    it('should log error with unknown route when not in development', function (done) {
      const req = {
        authedUser: adminUserWithRootAccess,
        method: 'PUT',
        route: { path: '/articles/:id/publish' }
      }
      const res = { json: function () {} }
      const next = function () {}

      serviceLocator.env = 'production'
      serviceLocator.logger.warn = function (message) {
        assert.strictEqual(
          message,
          'Unknown action for method: PUT path: /articles/:id/publish'
        )
        serviceLocator.env = 'development'
        serviceLocator.logger.warn = function () {}
        done()
      }

      authorisationMiddleware = createAuthorisationMiddleware(
        serviceLocator,
        '/articles',
        'article'
      )
      authorisationMiddleware(req, res, next)
    })

    it('should 401 if a defaultAction doesnt have access', function (done) {
      const req = {
        authedUser: adminUserWithRootAccess,
        method: 'PUT',
        route: { path: '/articles/:id?' }
      }
      const res = {
        json: function (error) {
          assert.strictEqual(
            error.error,
            'You are not authorised to update article'
          )
          done()
        }
      }
      const next = function () {}

      res.status = function (code) {
        assert.strictEqual(code, 401)
        return res
      }

      authorisationMiddleware = createAuthorisationMiddleware(
        serviceLocator,
        '/articles',
        'article'
      )
      authorisationMiddleware(req, res, next)
    })

    it('should 401 if a custom action doesnt have access', function (done) {
      const req = {
        authedUser: adminUserWithEditorAccess,
        method: 'PUT',
        route: { path: '/articles/:id/publish' }
      }
      const res = {
        json: function (error) {
          assert.strictEqual(
            error.error,
            'You are not authorised to publish article'
          )
          done()
        }
      }
      const next = function () {}
      const customActions = { 'PUT:/articles/:id/publish': 'publish' }

      res.status = function (code) {
        assert.strictEqual(code, 401)
        return res
      }

      authorisationMiddleware = createAuthorisationMiddleware(
        serviceLocator,
        '/articles',
        'article',
        customActions
      )
      authorisationMiddleware(req, res, next)
    })
  })
})
