const assert = require('assert')
const serviceLocator = require('service-locator')()
const logger = require('mc-logger')
const initGetAuthedUserMiddleware = require('../../../lib/middleware/authenticated-user-resolver')

describe('Get Authed User Middleware', function () {
  var getAuthedUserMiddleware

  before(function () {
    serviceLocator.register('logger', logger)
    serviceLocator.administratorService = {
      findOne: function (id, cb) {
        cb()
      }
    }
  })

  it('should set req.authedUser when req.authedClient exists', function (done) {
    const req = { authedClient: '1' }
    const res = {}
    const next = function () {
      assert.strictEqual(req.authedUser._id, '1')
      done()
    }

    serviceLocator.administratorService.findOne = function (query, cb) {
      assert.deepStrictEqual(query, { _id: '1' })
      cb(null, { _id: '1' })
    }

    getAuthedUserMiddleware = initGetAuthedUserMiddleware(serviceLocator)
    getAuthedUserMiddleware(req, res, next)
  })

  it('should call next with error when error occurs', function (done) {
    const req = { authedClient: '1' }
    const res = {}
    const next = function (error) {
      assert.strictEqual(error.message, 'Something wrong happened')
      done()
    }

    serviceLocator.administratorService.findOne = function (query, cb) {
      cb(new Error('Something wrong happened'))
    }

    getAuthedUserMiddleware = initGetAuthedUserMiddleware(serviceLocator)
    getAuthedUserMiddleware(req, res, next)
  })
})
