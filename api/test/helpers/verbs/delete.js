module.exports = del
const request = require('supertest')
const assert = require('assert')
const assignOrInvoke = require('../assign-or-invoke')
const createSignature = require('cf-signature')

function del(serviceLocator, name, rootUrl, fixtures) {
  let service
  const date = new Date().toUTCString()

  describe('DELETE ' + rootUrl + '/{id} to delete a ' + name, function () {
    var id

    afterEach(function (done) {
      // Clean out all the items
      service.deleteMany({}, done)
    })

    before(function () {
      service = serviceLocator[name + 'Service']
    })

    before(function (done) {
      // Create one valid item
      service.create(assignOrInvoke(fixtures.validNewModel), function (
        error,
        model
      ) {
        if (error) return done(error)
        id = model._id
        done()
      })
    })

    it('should return 204 on success', function (done) {
      request(serviceLocator.server)
        .del(rootUrl + '/' + id)
        .send(assignOrInvoke(fixtures.validNewModel))
        .set('Accept', 'application/json')
        .set('x-cf-date', date)
        .set(
          'Authorization',
          'Catfish ' +
            serviceLocator.authenticatedAdministrator._id +
            ':' +
            createSignature(
              serviceLocator.authenticatedAdministrator.key,
              'DELETE',
              'application/json',
              date,
              rootUrl + '/' + id
            )
        )
        .end(function (error, res) {
          assert.strictEqual(error, null)
          assert.strictEqual(res.statusCode, 204)
          done()
        })
    })

    it('should return 404 if id not found', function (done) {
      var date = new Date().toUTCString()
      request(serviceLocator.server)
        .del(rootUrl + '/999')
        .set('Accept', 'application/json')
        .set('x-cf-date', date)
        .expect(404)
        .end(function () {
          done()
        })
    })
  })
}
