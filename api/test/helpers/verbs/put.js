module.exports = put
const request = require('supertest')
const async = require('async')
const assert = require('assert-diff')
const extend = require('lodash.assign')
const sequentialObjectEql = require('../sequential-object-eql')
const assignOrInvoke = require('../assign-or-invoke')
const createSignature = require('cf-signature')

function put(serviceLocator, name, rootUrl, fixtures) {
  let service
  const date = new Date().toUTCString()

  describe('PUT ' + rootUrl + ' to update an array of ' + name, function () {
    let ids

    before(function () {
      service = serviceLocator[name + 'Service']
    })

    afterEach(function (done) {
      // Clean out all the items
      service.deleteMany({}, done)
    })

    beforeEach(function (done) {
      ids = []
      async.times(
        2,
        function (n, next) {
          service.create(assignOrInvoke(fixtures.validNewModel), function (
            error,
            model
          ) {
            if (error) {
              console.error(error.errors && error.errors)
              return done(error)
            }
            ids.push(model._id)
            next()
          })
        },
        done
      )
    })

    it('should return 200 on success when passed an array of objects', function (done) {
      const validModelA = extend(
        {},
        assignOrInvoke(fixtures.completeModelResponseWithAllData),
        { _id: '' + ids[0] }
      )
      const validModelB = extend(
        {},
        assignOrInvoke(fixtures.completeModelResponseWithAllData),
        { _id: '' + ids[1] }
      )

      request(serviceLocator.server)
        .put(rootUrl)
        .send([validModelA, validModelB])
        .set('Accept', 'application/json')
        .set('x-cf-date', date)
        .set(
          'Authorization',
          'Catfish ' +
            serviceLocator.authenticatedAdministrator._id +
            ':' +
            createSignature(
              serviceLocator.authenticatedAdministrator.key,
              'PUT',
              'application/json',
              date,
              rootUrl
            )
        )
        .expect(200)
        .expect('Content-Type', /json/)
        .end(function (error, res) {
          if (error) return done(error)
          assert.strictEqual(Array.isArray(res.body), true)
          const model = res.body

          sequentialObjectEql(model[0], validModelA)
          sequentialObjectEql(model[1], validModelB)

          done()
        })
    })

    it('should return 400 on validation failure with an array of objects', function (done) {
      fixtures.invalidModel._id = ids[0]
      const completeValidModel = assignOrInvoke(fixtures.completeValidModel)
      completeValidModel._id = ids[1]

      request(serviceLocator.server)
        .put(rootUrl)
        .send([fixtures.invalidModel, completeValidModel])
        .set('Accept', 'application/json')
        .set('x-cf-date', date)
        .set(
          'Authorization',
          'Catfish ' +
            serviceLocator.authenticatedAdministrator._id +
            ':' +
            createSignature(
              serviceLocator.authenticatedAdministrator.key,
              'PUT',
              'application/json',
              date,
              rootUrl
            )
        )
        .expect(400)
        .expect('Content-Type', /json/)
        .end(function (error, res) {
          if (error) return done(error)
          assert.strictEqual(Array.isArray(res.body), true)
          // Checking that _id is also returned with errors object
          sequentialObjectEql(
            res.body[0],
            extend({ _id: ids[0] }, fixtures.createErrorResponse)
          )
          const completeModelResponseWithAllData = assignOrInvoke(
            fixtures.completeModelResponseWithAllData
          )

          sequentialObjectEql(
            res.body[1],
            extend({ _id: ids[1] }, completeModelResponseWithAllData)
          )

          done()
        })
    })
  })

  describe('PUT ' + rootUrl + '/{id} to update a ' + name, function () {
    let id
    before(function (done) {
      // Create one valid item
      service.create(assignOrInvoke(fixtures.validNewModel), function (
        error,
        model
      ) {
        if (error) return done(error)
        id = model._id
        done()
      })
    })

    it('should return 200 on success', function (done) {
      const completeValidModel = assignOrInvoke(fixtures.completeValidModel)
      const completeModelResponseWithAllData = assignOrInvoke(
        fixtures.completeModelResponseWithAllData
      )

      completeValidModel._id = id
      completeModelResponseWithAllData._id = id

      request(serviceLocator.server)
        .put(rootUrl + '/' + id)
        .send(completeValidModel)
        .set('x-cf-date', date)
        .set(
          'Authorization',
          'Catfish ' +
            serviceLocator.authenticatedAdministrator._id +
            ':' +
            createSignature(
              serviceLocator.authenticatedAdministrator.key,
              'PUT',
              'application/json',
              date,
              rootUrl + '/' + id
            )
        )
        .set('Accept', 'application/json')
        .expect(200)
        .expect('Content-Type', /json/)
        .end(function (error, res) {
          if (error) return done(error)
          sequentialObjectEql(res.body, completeModelResponseWithAllData)
          done()
        })
    })

    it('should return 400 on id mismatch', function (done) {
      const completeValidModel = assignOrInvoke(fixtures.completeValidModel)
      completeValidModel._id = id

      request(serviceLocator.server)
        .put(rootUrl + '/' + 9999)
        .send(completeValidModel)
        .set('Accept', 'application/json')
        .set('x-cf-date', date)
        .set(
          'Authorization',
          'Catfish ' +
            serviceLocator.authenticatedAdministrator._id +
            ':' +
            createSignature(
              serviceLocator.authenticatedAdministrator.key,
              'PUT',
              'application/json',
              date,
              rootUrl + '/' + 9999
            )
        )
        .expect(400)
        .expect('Content-Type', /json/)
        .end(function (error, res) {
          if (error) return done(error)
          assert.strictEqual(res.body.errors.id, 'Mismatch')
          done()
        })
    })

    it('should return 400 on validation failure', function (done) {
      fixtures.invalidModel._id = id
      request(serviceLocator.server)
        .put(rootUrl + '/' + id)
        .send(fixtures.invalidModel)
        .set('Accept', 'application/json')
        .set('x-cf-date', date)
        .set(
          'Authorization',
          'Catfish ' +
            serviceLocator.authenticatedAdministrator._id +
            ':' +
            createSignature(
              serviceLocator.authenticatedAdministrator.key,
              'PUT',
              'application/json',
              date,
              rootUrl + '/' + id
            )
        )

        .expect(400)
        .expect('Content-Type', /json/)
        .end(done)
    })
  })
}
