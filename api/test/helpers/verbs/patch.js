module.exports = patch
const request = require('supertest')
const sequentialObjectEql = require('../sequential-object-eql')
const extend = require('lodash.assign')
const assignOrInvoke = require('../assign-or-invoke')
const createSignature = require('cf-signature')

function patch(serviceLocator, name, rootUrl, fixtures) {
  let service
  const date = new Date().toUTCString()

  describe(
    'PATCH ' + rootUrl + '/{id} to perform a partial update on a ' + name,
    function () {
      before(function () {
        service = serviceLocator[name + 'Service']
      })

      afterEach(function (done) {
        // Clean out all the items
        service.deleteMany({}, done)
      })

      var id
      before(function (done) {
        // Create one valid item
        service.create(assignOrInvoke(fixtures.validNewModel), function (
          error,
          model
        ) {
          if (error) return done(error)
          id = model._id
          done()
        })
      })

      it('should return 200 on success', function (done) {
        var result = extend(
          {},
          assignOrInvoke(fixtures.validFirstSavedModel),
          assignOrInvoke(fixtures.validPartialModel),
          { _id: '' + id }
        )
        request(serviceLocator.server)
          .patch(rootUrl + '/' + id)
          .send(extend({}, assignOrInvoke(fixtures.validPartialModel)))
          .set('Accept', 'application/json')
          .set('x-cf-date', date)
          .set(
            'Authorization',
            'Catfish ' +
              serviceLocator.authenticatedAdministrator._id +
              ':' +
              createSignature(
                serviceLocator.authenticatedAdministrator.key,
                'PATCH',
                'application/json',
                date,
                rootUrl + '/' + id
              )
          )
          .expect(200)
          .expect('Content-Type', /json/)
          .end(function (error, res) {
            if (error) return done(error)
            sequentialObjectEql(res.body, result)
            done()
          })
      })
    }
  )
}
