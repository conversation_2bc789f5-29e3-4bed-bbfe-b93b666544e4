module.exports = get
const hat = require('hat')
const request = require('supertest')
const assert = require('assert')
const sequentialObjectEql = require('../sequential-object-eql')
const async = require('async')
const extend = require('lodash.assign')
const assignOrInvoke = require('../assign-or-invoke')
const createSignature = require('cf-signature')

function get(serviceLocator, name, rootUrl, fixtures) {
  var service

  describe('GET ' + rootUrl + ' to get a ' + name + ' list:', function () {
    afterEach(function (done) {
      // Clean out all the items
      var query = {}
      query[fixtures.filterColumn] = fixtures.filterValue
      service.deleteMany(query, done)
    })

    before(function () {
      service = serviceLocator[name + 'Service']
    })

    var id
    beforeEach(function (done) {
      // Create one valid item
      var fixture = assignOrInvoke(fixtures.validNewModel)
      if (fixture.headline) fixture.slug = hat()
      service.create(fixture, function (error, model) {
        id = model._id
        if (error) console.error(error.errors)
        done(error)
      })
    })

    it('should return 200 and list of items', function (done) {
      var date = new Date().toUTCString()
      request(serviceLocator.server)
        .get(rootUrl)
        .set('Accept', 'application/json')
        .set('x-cf-date', date)
        .set(
          'Authorization',
          'Catfish ' +
            serviceLocator.authenticatedAdministrator._id +
            ':' +
            createSignature(
              serviceLocator.authenticatedAdministrator.key,
              'GET',
              '',
              date,
              rootUrl
            )
        )
        .expect(200)
        .expect('Content-Type', /json/)
        .end(function (error, res) {
          if (error) return done(error)
          sequentialObjectEql(
            res.body.results[0],
            assignOrInvoke(fixtures.validFirstSavedModel)
          )

          assert(res.body.page, 'missing page property')
          assert(res.body.pageSize, 'missing pageSize property')
          assert(res.body.totalItems, 'missing totalItems property')

          done()
        })
    })

    it('should return 406 for unexpected Content-type', function (done) {
      var date = new Date().toUTCString()
      request(serviceLocator.server)
        .get(rootUrl)
        .set('Accept', 'application/xml')
        .set('x-cf-date', date)
        .set(
          'Authorization',
          'Catfish ' +
            serviceLocator.authenticatedAdministrator._id +
            ':' +
            createSignature(
              serviceLocator.authenticatedAdministrator.key,
              'GET',
              '',
              date,
              rootUrl
            )
        )
        .expect(406)
        .expect('Content-Type', /plain/)
        .end(done)
    })

    it('should return 200 when Content-type is an acceptable priority list', function (done) {
      var date = new Date().toUTCString()
      request(serviceLocator.server)
        .get(rootUrl)
        .set('Accept', 'application/json, text/javascript, */*; q=0.01')
        .set('x-cf-date', date)
        .set(
          'Authorization',
          'Catfish ' +
            serviceLocator.authenticatedAdministrator._id +
            ':' +
            createSignature(
              serviceLocator.authenticatedAdministrator.key,
              'GET',
              '',
              date,
              rootUrl
            )
        )
        .expect(200)
        .expect('Content-Type', /json/)
        .end(done)
    })

    describe('ordering and filtering', function () {
      beforeEach(function (done) {
        // Create one extra valid item
        async.times(
          2,
          function (n, next) {
            var fixture = assignOrInvoke(fixtures.validNewModel)

            service.create(fixture, function () {
              next()
            })
          },
          function () {
            done()
          }
        )
      })

      it('should return an ascending result set ordered by _id when a valid sort key is passed', function (done) {
        const url = rootUrl + '?sort=' + encodeURIComponent('["_id"]')
        const date = new Date().toUTCString()
        request(serviceLocator.server)
          .get(url)
          .set('Accept', 'application/json')
          .set('x-cf-date', date)
          .set(
            'Authorization',
            'Catfish ' +
              serviceLocator.authenticatedAdministrator._id +
              ':' +
              createSignature(
                serviceLocator.authenticatedAdministrator.key,
                'GET',
                '',
                date,
                url
              )
          )
          .expect(200)
          .expect('Content-Type', /json/)
          .end(function (error, res) {
            if (error) return done(error)
            assert.strictEqual(res.statusCode, 200)
            assert(res.body.results.length > 1, 'should be more than 1 result')
            assert(
              res.body.results[0]._id < res.body.results[1]._id,
              'result 0 should have a lower id than result 1'
            )
            done()
          })
      })

      it('should return a descending result set when a sort direction is passed which is not "asc"', function (done) {
        const url = rootUrl + '?sort=' + encodeURIComponent('["_id", "desc"]')
        const date = new Date().toUTCString()
        request(serviceLocator.server)
          .get(url)
          .set('Accept', 'application/json')
          .set('x-cf-date', date)
          .set(
            'Authorization',
            'Catfish ' +
              serviceLocator.authenticatedAdministrator._id +
              ':' +
              createSignature(
                serviceLocator.authenticatedAdministrator.key,
                'GET',
                '',
                date,
                url
              )
          )
          .expect(200)
          .expect('Content-Type', /json/)
          .end(function (error, res) {
            if (error) return done(error)
            assert(res.body.results.length > 1, 'should be more than 1 result')
            assert(
              res.body.results[1]._id < res.body.results[0]._id,
              'result 1 should have a lower id than result 0'
            )
            done()
          })
      })

      it('should return a filtered and descending result set when a sort and filter is supplied', function (done) {
        const filterColumn = fixtures.sortColumns.filterColumn
        const filterValue = fixtures.sortColumns.filterValue
        const sortColumn = fixtures.sortColumns.sortColumn
        const url =
          rootUrl +
          '?filter=' +
          encodeURIComponent('{"' + filterColumn + '":"' + filterValue + '"}') +
          '&sort=' +
          encodeURIComponent('["' + sortColumn + '", "desc"]')
        const date = new Date().toUTCString()

        request(serviceLocator.server)
          .get(url)
          .set('Accept', 'application/json')
          .set('x-cf-date', date)
          .set(
            'Authorization',
            'Catfish ' +
              serviceLocator.authenticatedAdministrator._id +
              ':' +
              createSignature(
                serviceLocator.authenticatedAdministrator.key,
                'GET',
                '',
                date,
                url
              )
          )
          .expect(200)
          .expect('Content-Type', /json/)
          .end(function (error, res) {
            if (error) return done(error)
            assert.strictEqual(
              res.body.results.length,
              3,
              JSON.stringify(res.body, null, 2)
            )
            assert(
              res.body.results[1]._id < res.body.results[0]._id,
              'result 1 should have a lower id than result 0'
            )
            done()
          })
      })

      it('should return 400 when an invalid GET parameter is passed', function (done) {
        const url = rootUrl + '?sort=_'
        const date = new Date().toUTCString()
        request(serviceLocator.server)
          .get(url)
          .set('Accept', 'application/json')
          .set('x-cf-date', date)
          .set(
            'Authorization',
            'Catfish ' +
              serviceLocator.authenticatedAdministrator._id +
              ':' +
              createSignature(
                serviceLocator.authenticatedAdministrator.key,
                'GET',
                '',
                date,
                url
              )
          )
          .expect(400)
          .expect('Content-Type', /json/)
          .end(done)
      })

      it('should return a filtered result set when a valid GET parameter is passed', function (done) {
        const url =
          rootUrl + '?filter=' + encodeURIComponent('{"_id":"' + id + '"}')
        const date = new Date().toUTCString()
        request(serviceLocator.server)
          .get(url)
          .set('Accept', 'application/json')
          .set('x-cf-date', date)
          .set(
            'Authorization',
            'Catfish ' +
              serviceLocator.authenticatedAdministrator._id +
              ':' +
              createSignature(
                serviceLocator.authenticatedAdministrator.key,
                'GET',
                '',
                date,
                url
              )
          )
          .expect(200)
          .expect('Content-Type', /json/)
          .end(function (error, res) {
            if (error) return done(error)
            assert.strictEqual(res.body.results.length, 1)

            var validModel = extend(
              assignOrInvoke(fixtures.validFirstSavedModel),
              { _id: id }
            )

            sequentialObjectEql(validModel, res.body.results[0])

            assert(res.body.page, 'missing page property')
            assert(res.body.pageSize, 'missing pageSize property')
            assert('number', typeof res.body.totalItems)
            done()
          })
      })

      it('should return a 400 when filter is not a JSON string', function (done) {
        const url = rootUrl + '?filter=1'
        const date = new Date().toUTCString()
        request(serviceLocator.server)
          .get(url)
          .set('Accept', 'application/json')
          .set('x-cf-date', date)
          .set(
            'Authorization',
            'Catfish ' +
              serviceLocator.authenticatedAdministrator._id +
              ':' +
              createSignature(
                serviceLocator.authenticatedAdministrator.key,
                'GET',
                '',
                date,
                url
              )
          )
          .expect(400)
          .expect('Content-Type', /json/)
          .end(done)
      })

      it('should return a 400 when invalid JSON is passed as a filter', function (done) {
        const url =
          rootUrl + '?filter=' + encodeURIComponent('{_id:' + id + '}')
        const date = new Date().toUTCString()
        request(serviceLocator.server)
          .get(url)
          .set('Accept', 'application/json')
          .set('x-cf-date', date)
          .set(
            'Authorization',
            'Catfish ' +
              serviceLocator.authenticatedAdministrator._id +
              ':' +
              createSignature(
                serviceLocator.authenticatedAdministrator.key,
                'GET',
                '',
                date,
                url
              )
          )
          .expect(400)
          .expect('Content-Type', /json/)
          .end(done)
      })
    })

    describe('pagination', function () {
      // This is skipped until the persistence engine can do limit
      it.skip('should limit the number of responses to the page size', function (done) {
        var date = new Date().toUTCString()

        request(serviceLocator.server)
          .get(rootUrl + '?pagination={"pageSize":0}')
          .set('Accept', 'application/json')
          .set('x-cf-date', date)
          .set(
            'Authorization',
            'Catfish ' +
              serviceLocator.authenticatedAdministrator._id +
              ':' +
              createSignature(
                serviceLocator.authenticatedAdministrator.key,
                'GET',
                '',
                date,
                rootUrl
              )
          )
          .expect(200)
          .expect('Content-Type', /json/)
          .end(function (error, res) {
            if (error) return done(error)
            assert.strictEqual(res.body.results.length, 0)
            done()
          })
      })
    })
  })
}
