module.exports = post
const request = require('supertest')
const assert = require('assert')
const sequentialObjectEql = require('../sequential-object-eql')
const extend = require('lodash.assign')
const assignOrInvoke = require('../assign-or-invoke')
const createSignature = require('cf-signature')

function post(serviceLocator, name, rootUrl, fixtures) {
  var service

  describe('POST ' + rootUrl + ' to create a ' + name, function () {
    before(function () {
      service = serviceLocator[name + 'Service']
    })

    afterEach(function (done) {
      // Clean out all the items
      service.deleteMany({}, done)
    })

    it('should return 201 on success', function (done) {
      const fixture = assignOrInvoke(fixtures.validNewModel)
      const date = new Date().toUTCString()

      request(serviceLocator.server)
        .post(rootUrl)
        .set('Accept', 'application/json')

        .set('x-cf-date', date)
        .set(
          'Authorization',
          'Catfish ' +
            serviceLocator.authenticatedAdministrator._id +
            ':' +
            createSignature(
              serviceLocator.authenticatedAdministrator.key,
              'POST',
              'application/json',
              date,
              rootUrl
            )
        )

        .send(fixture)
        .expect(201)
        .end(function (error, res) {
          if (error) return done(error)
          assert.strictEqual(
            res.headers['content-type'],
            'application/json; charset=utf-8'
          )
          const model = res.body
          const validModel = extend(
            assignOrInvoke(fixtures.validFirstSavedModel),
            { _id: model._id }
          )
          sequentialObjectEql(model, validModel)
          done()
        })
    })

    it('should return 400 on validation failure', function (done) {
      var date = new Date().toUTCString()
      request(serviceLocator.server)
        .post(rootUrl)
        .send(fixtures.invalidModel)
        .set('Accept', 'application/json')
        .set('x-cf-date', date)
        .set(
          'Authorization',
          'Catfish ' +
            serviceLocator.authenticatedAdministrator._id +
            ':' +
            createSignature(
              serviceLocator.authenticatedAdministrator.key,
              'POST',
              'application/json',
              date,
              rootUrl
            )
        )
        .expect(400)
        .expect('Content-Type', /json/)
        .end(function (error, res) {
          if (error) return done(error)
          assert.deepStrictEqual(res.body, fixtures.createErrorResponse)
          done()
        })
    })

    it('should return 400 on content-type error', function (done) {
      request(serviceLocator.server)
        .post(rootUrl)
        .set('Accept', 'application/json')
        .set('Content-Type', 'text/plain')
        .send('')
        .expect(400)
        .expect('Content-Type', /json/)
        .end(function (error, res) {
          if (error) return done(error)
          assert.strictEqual(
            res.body.error,
            'API does not support text/plain content-type'
          )
          done()
        })
    })

    it('should return 400 on content type mismatch', function (done) {
      request(serviceLocator.server)
        .post(rootUrl)
        .set('Content-Type', 'application/json')
        .set('Accept', 'application/json')
        .send('This is not valid JSON')
        .expect(400)
        .end(function (error, res) {
          if (error) return done(error)
          assert.strictEqual(/Unexpected token T/.test(res.body.error), true)
          done()
        })
    })

    it('should return all properties defined in the schema', function (done) {
      var date = new Date().toUTCString()
      request(serviceLocator.server)
        .post(rootUrl)
        .send(assignOrInvoke(fixtures.completeValidModel))
        .set('Accept', 'application/json')
        .set('x-cf-date', date)
        .set(
          'Authorization',
          'Catfish ' +
            serviceLocator.authenticatedAdministrator._id +
            ':' +
            createSignature(
              serviceLocator.authenticatedAdministrator.key,
              'POST',
              'application/json',
              date,
              rootUrl
            )
        )
        .expect(201)
        .expect('Content-Type', /json/)
        .end(function (error, res) {
          if (error) return done(error)
          var model = res.body
          assert(model._id, 'id property should exist')
          Object.keys(assignOrInvoke(fixtures.completeValidModel)).forEach(
            function (key) {
              assert(
                Object.prototype.hasOwnProperty.call(model, key),
                key + ' property should exist'
              )
            }
          )

          done()
        })
    })

    it('should return the same values as posted', function (done) {
      const completeModelResponseWithAllData = assignOrInvoke(
        fixtures.completeModelResponseWithAllData
      )
      const date = new Date().toUTCString()
      request(serviceLocator.server)
        .post(rootUrl)
        .send(assignOrInvoke(fixtures.completeValidModel))
        .set('Accept', 'application/json')
        .set('x-cf-date', date)
        .set(
          'Authorization',
          'Catfish ' +
            serviceLocator.authenticatedAdministrator._id +
            ':' +
            createSignature(
              serviceLocator.authenticatedAdministrator.key,
              'POST',
              'application/json',
              date,
              rootUrl
            )
        )
        .expect(201)
        .expect('Content-Type', /json/)
        .end(function (error, res) {
          if (error) return done(error)
          var model = res.body
          sequentialObjectEql(completeModelResponseWithAllData, model, true)

          done()
        })
    })
  })
}
