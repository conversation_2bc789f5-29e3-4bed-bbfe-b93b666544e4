module.exports = crudTests

const tests = {
  get: require('./verbs/get'),
  post: require('./verbs/post'),
  put: require('./verbs/put'),
  patch: require('./verbs/patch'),
  delete: require('./verbs/delete')
}
const administratorFixtures = require('../../../components/service/administrator/test/fixtures/')
const extend = require('lodash.assign')
const assert = require('assert')
const assignOrInvoke = require('./assign-or-invoke')

function crudTests(serviceLocator, name, rootUrl, fixtures, verbs) {
  if (!Array.isArray(verbs)) verbs = ['get', 'put', 'post', 'patch', 'delete']

  beforeEach(function (done) {
    const newAdministrator = assignOrInvoke(administratorFixtures.validNewModel)
    const newRole = { name: 'root', grants: { '*': ['*'] } }
    serviceLocator.roleService.create(newRole, function () {
      serviceLocator.administratorService.create(
        extend({}, newAdministrator, {
          emailAddress:
            Math.round(Math.random() * 1000000).toString(36) +
            '-<EMAIL>',
          firstName: 'Test',
          roles: ['root']
        }),
        function (error, administrator) {
          if (error) return done(error)
          serviceLocator.administratorService.authenticate(
            {
              identity: administrator.emailAddress,
              password: newAdministrator.password
            },
            function (error, administrator) {
              if (error) return done(error)
              assert(administrator.key, 'key should exist')

              // Hang this here to use in tests
              serviceLocator.authenticatedAdministrator = administrator
              done()
            }
          )
        }
      )
    })
  })

  describe.only('REST API for ' + name + ':', function () {
    verbs.forEach(function (verb) {
      tests[verb](serviceLocator, name, rootUrl, fixtures)
    })
  })
}
