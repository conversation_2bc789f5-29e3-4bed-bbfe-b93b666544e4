const join = require('path').join
const createConfigury = require('@clocklimited/configury')
const configury = createConfigury(join(__dirname, '/../../config.json'))
const config = configury(process.env.NODE_ENV)
const UberCache = require('uber-cache')
const mclogger = require('mc-logger')
const mongodbUri = require('mongodb-uri')
const configurySecrets = createConfigury(join(__dirname, '/../../secrets.json'))
const secrets = configurySecrets(process.env.NODE_ENV)

module.exports = function () {
  const serviceLocator = require('service-locator')()
  const uriParts = mongodbUri.parse(
    process.env.MONGO_URL ||
      process.env.NF_DATABASE_MONGO_SRV ||
      'mongodb://localhost:27017/test'
  )

  uriParts.database = 'test' + Math.round(Math.random() * 1000000).toString(36)

  config.databaseUrl = mongodbUri.format(uriParts)

  serviceLocator
    .register('logger', mclogger)
    .register(
      'config',
      Object.assign({}, config, {
        darkroom: {
          url: 'https://dr.io',
          salt: 'salty salamanders',
          key: 'keyey keys'
        }
      })
    )
    .register('secrets', secrets)
    .register('mailer', { send: () => {} })
    .register('cache', new UberCache())

  serviceLocator.config.adminHashType = 'sha1'

  return serviceLocator
}
