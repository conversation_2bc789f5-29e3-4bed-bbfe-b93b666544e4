const parseQueryString = require('@clocklimited/cf-crud-service-api-builder/parse-query-string')
const createfilterParser = require('@clocklimited/cf-crud-service-api-builder/filter-parser')
const escapeRegExp = require('lodash.escaperegexp')

const createRegexSearchEndpoint = ({
  serviceLocator: { logger, router },
  service,
  path,
  property = 'name',
  middleware
}) => {
  const parseFilter = createfilterParser(service.schema)

  const createSearchQuery = (keywords) => {
    const trimmedKeywords = keywords.trim()
    if (!trimmedKeywords || !trimmedKeywords.length) return {}
    const terms = [trimmedKeywords.trim(), ...trimmedKeywords.trim().split(' ')]
      .map(escapeRegExp)
      .join('|')
    const query = new RegExp(`(${terms})`, 'i')
    return { [property]: query }
  }

  router.get(path, middleware, parseQueryString, (req, res) => {
    logger.debug('GET received (Search)', JSON.stringify(req.query))

    const { keywords, filter: rawFilter, sort, pagination } = req.query
    const sortProperty =
      sort && sort[0][0] !== 'score' ? sort : [[property, 'asc']]
    const options = { sort: sortProperty, projection: req.query.projection }

    if (!isNaN(pagination.pageSize)) {
      options.limit = pagination.pageSize
      if (!isNaN(pagination.page)) {
        options.skip = (pagination.page - 1) * pagination.pageSize
      }
    }

    const filter = {
      ...parseFilter(rawFilter),
      ...createSearchQuery(keywords)
    }

    service.search('', filter, options, (error, data, count) => {
      if (error) {
        logger.error(error.stack)
        return res.status(400).json(error)
      }

      res.json({
        results: data,
        page: pagination.page,
        pageSize: pagination.pageSize,
        totalItems: count
      })
    })
  })
}

module.exports = createRegexSearchEndpoint
