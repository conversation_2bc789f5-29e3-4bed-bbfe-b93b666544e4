const debug = require('debug')('schemata-graphql-adaptor')
const {
  GraphQLFloat,
  GraphQLString,
  GraphQLBoolean,
  GraphQLList
} = require('graphql')

const { GraphQLDateTime } = require('graphql-iso-date')

const mapType = (types, ObjectType, postfix, convertSchemataToGraphQl) => (
  type
) => {
  if (type.arraySchema) {
    return new GraphQLList(
      convertSchemataToGraphQl(type.arraySchema, types, ObjectType, postfix)
    )
  }
  if (type.name === 'Date') return GraphQLDateTime
  if (type.name === 'Boolean') return GraphQLBoolean
  if (type.name === 'String') return GraphQLString
  if (type.name === 'Number') return GraphQLFloat
  if (type.name === 'Array') {
    debug(
      'Arrays are converted to a `new GraphQLList(GraphQLString)` use `schemata.Array` to create defined mapping.'
    )
    return new GraphQLList(GraphQLString)
  }
  if (type.name === 'Object') {
    debug(
      'Object properties can not be converted. Use another `schemata` as type. Omitting'
    )
    return null
  }
  // Must be schemata
  return convertSchemataToGraphQl(type, types, ObjectType, postfix)
}

module.exports = mapType
