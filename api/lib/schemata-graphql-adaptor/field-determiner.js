const { GraphQLList, GraphQLUnionType } = require('graphql')

const determineFields = (mapType) => (properties) => {
  const handleUnion = (property) => {
    const unionType = new GraphQLUnionType({
      name: property.unionName,
      types: property.unionTypes.map(mapType)
    })
    return {
      type: Array.isArray(property.unionTypes)
        ? new GraphQLList(unionType)
        : unionType,
      resolve: property.resolve
    }
  }

  return Object.keys(properties).reduce((fields, property) => {
    if (properties[property].unionName && properties[property].unionTypes) {
      fields[property] = handleUnion(properties[property])
    } else {
      let rawType = properties[property].type
      // if we have a custom resolveType, it should only be a schemata object, or a function that returns one
      if (properties[property].resolveType) {
        rawType = properties[property].resolveType.getName
          ? properties[property].resolveType
          : properties[property].resolveType()
      }

      const type = mapType(rawType)
      if (type) {
        fields[property] = {
          type,
          resolve: properties[property].resolve,
          args: properties[property].resolveArgs
        }
      }
    }
    return fields
  }, {})
}

module.exports = determineFields
