const { GraphQLObjectType, GraphQLInterfaceType } = require('graphql')
const createFieldDeterminer = require('./field-determiner')
const createTypeMapper = require('./type-mapper')

const convertSchemataToGraphQl = (
  schema,
  types = {},
  ObjectType = GraphQLObjectType,
  postfix = ''
) => {
  const mapType = createTypeMapper(
    types,
    ObjectType,
    postfix,
    convertSchemataToGraphQl
  )
  const determineFields = createFieldDeterminer(mapType)

  if (!schema.getName) {
    throw new Error(
      `Schema is not a valid schemata instance: ${JSON.stringify(schema)}`
    )
  }

  const name = schema.getName().replace(/\s+/g, '') + postfix
  const description = schema.getDescription()
  const allProperties = schema.getProperties()
  // Remove private properties
  const properties = Object.keys(allProperties).reduce((props, key) => {
    const property = allProperties[key]
    if (!property.private) {
      props[key] = property
    }
    return props
  }, {})
  const fields = determineFields.bind(null, properties)

  if (types[name]) return types[name]

  const setupInterface = (schema) => {
    const graphInterface = convertSchemataToGraphQl(
      schema,
      types,
      GraphQLInterfaceType,
      postfix
    )
    return graphInterface
  }

  const newType = new ObjectType({
    name,
    fields,
    description,
    interfaces: schema.implements
      ? schema.implements.map(setupInterface)
      : undefined,
    isTypeOf: schema.isTypeOf ? schema.isTypeOf : undefined
  })
  types[newType.name] = newType

  return newType
}

module.exports = convertSchemataToGraphQl
