const convert = require('..')
const schemata = require('@clocklimited/schemata')
const assert = require('assert')
const { GraphQLObjectType } = require('graphql')

const createAnnoymousSchemata = (properties) =>
  schemata({
    name: 'Annoumous',
    properties
  })

const person = schemata({
  name: 'Person Who Is Valid',
  properties: {
    name: {
      type: String
    },
    age: {
      type: Number
    },
    active: {
      type: Boolean
    },
    birthday: {
      type: Date
    },
    tags: {
      type: Array
    }
  }
})

const contact = schemata({
  name: 'Contact',
  properties: {
    ...person.getProperties(),
    friends: {
      type: schemata.Array(person)
    }
  }
})

const widgetArea = schemata({
  name: 'WidgetArea',
  properties: {
    widgets: {
      type: Array,
      unionName: 'AbstractWidget',
      unionTypes: []
    }
  }
})

const baseWidget = schemata({
  name: 'Widget',
  properties: {
    id: {
      type: String
    }
  }
})

const textWidget = schemata({
  name: 'TextWidget',
  properties: {
    id: {
      type: String
    },
    text: {
      type: String
    }
  }
})
textWidget.implements = [baseWidget]

describe('schemata-graphql-adaptor', () => {
  it('should convert valid schemata', () => {
    const graphQlSchema = convert(person)
    assert(graphQlSchema instanceof GraphQLObjectType)
    assert.deepStrictEqual(
      Object.keys(graphQlSchema.getFields()),
      Object.keys(person.getProperties())
    )
  })

  it('should remove spaces from schemata name', () => {
    const graphQlSchema = convert(person)
    assert(graphQlSchema instanceof GraphQLObjectType)
    assert.deepStrictEqual(
      Object.keys(graphQlSchema.getFields()),
      Object.keys(person.getProperties())
    )
  })

  it('should convert String type', () => {
    const fields = convert(person).getFields()
    assert.strictEqual(fields.name.type.toString(), 'String')
  })

  it('should convert Number type', () => {
    const fields = convert(person).getFields()
    assert.strictEqual(fields.age.type.toString(), 'Float')
  })

  it('should convert Boolean type', () => {
    const fields = convert(person).getFields()
    assert.strictEqual(fields.active.type.toString(), 'Boolean')
  })

  it('should convert Date type', () => {
    const fields = convert(person).getFields()
    assert.strictEqual(fields.birthday.type.toString(), 'DateTime')
  })

  it('should convert Array type', () => {
    const fields = convert(person).getFields()
    assert.strictEqual(fields.tags.type.toString(), '[String]')
  })

  it('should omit if Object type is used', () => {
    const fields = convert(
      createAnnoymousSchemata({
        name: { type: String },
        widgets: { type: Object }
      })
    )
    assert.strictEqual(fields.widgets, undefined)
  })

  it('should convert Array schemata type', () => {
    const fields = convert(contact).getFields()
    assert.strictEqual(fields.friends.type.toString(), '[PersonWhoIsValid]')
  })

  it('should resolve unions', () => {
    const fields = convert(widgetArea).getFields()
    assert.strictEqual(fields.widgets.type.toString(), '[AbstractWidget]')
  })

  it('should resolve interfaces', () => {
    const converted = convert(textWidget)
    const interfaces = converted.getInterfaces()
    assert.strictEqual(interfaces[0].name, convert(baseWidget).name)
  })
})
