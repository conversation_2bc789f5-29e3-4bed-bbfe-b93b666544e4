const {
  buildSearchQuery,
  extendOptions
} = require('@clocklimited/cf-text-search')
const ObjectToCsvStream = require('object-to-csv-stream')
const { Transform } = require('stream')
const moment = require('moment')

module.exports = (
  { logger, config },
  {
    name,
    service,
    transform,
    embellish,
    keywords = '',
    filter = {},
    sort = [['dateCreated', 'asc']]
  }
) => (req, res) => {
  res.set('Content-Type', 'text/csv')
  const filename = `${name}-${moment().format(
    config.formats.isoFileSystem
  )}.csv`
  res.set('Content-Disposition', 'attachment; filename="' + filename + '"')

  logger.info(`Creating ${name} export, filename "${filename}"`)

  const embellishStream = new Transform({
    objectMode: true,
    transform(data, _, done) {
      if (embellish) return embellish(data, done)
      done(null, data)
    }
  })

  const escapeQuotes = (value) => {
    if (typeof value === 'string') {
      return value.replace(/"/g, '""')
    }
    return value
  }

  const mapFn = (entity) => {
    const line = Object.keys(transform).map((key) => {
      const map = transform[key]
      const value = typeof map === 'string' ? entity[map] : map(entity)
      // Replace null values with empty strings
      return [undefined, null].includes(value) ? '' : escapeQuotes(value)
    })
    return line
  }

  const headers = Object.keys(transform)
  const objectToCsvStream = new ObjectToCsvStream(headers, { mapFn })

  const sortProperty =
    sort && sort[0][0] === 'score' ? { score: { $meta: 'textScore' } } : sort
  const query = buildSearchQuery(keywords, filter)
  const options = extendOptions({ sort: sortProperty }, keywords)

  res.write('\uFEFF')
  service
    .find(query, options)
    .pipe(embellishStream)
    .pipe(objectToCsvStream)
    .pipe(res)
}
