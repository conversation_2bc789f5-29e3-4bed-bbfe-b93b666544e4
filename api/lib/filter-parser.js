const castProperty = require('@clocklimited/schemata').castProperty

module.exports = createFilterParser

function createFilterParser(schema) {
  const properties = schema.getProperties()
  function parseObject(object, parentKey) {
    var newObj = {}
    Object.keys(object).forEach(function (key) {
      let value = object[key]
      const ignoredTypes = [Object, Array]
      let type = null
      const newValue = []

      if (parentKey) {
        type = properties[parentKey].type
      } else {
        type = properties[key].type
      }

      // Skip ignored types and Schemata Arrays
      if (ignoredTypes.indexOf(type) === -1 && !type.arraySchema) {
        if (Array.isArray(value)) {
          value.forEach(function (item) {
            newValue.push(castProperty(type, item))
          })
          value = newValue
        } else if (typeof value === 'object') {
          value = parseObject(value, key)
        } else {
          value = castProperty(type, value)
        }
      }
      newObj[key] = value
    })
    return newObj
  }

  return parseObject
}
