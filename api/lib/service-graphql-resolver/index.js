const { GraphQLString } = require('graphql')
const { promisify } = require('util')

// Database options supported
const supportedOptions = [
  { name: 'limit', transform: null },
  { name: 'skip', transform: null },
  { name: 'sort', transform: JSON.parse }
]

// Copy supported k/v from existing object to new object
// Deconstructs args to remove limit and sort from query and schema
const transformObjects = (a, b, key, databaseStage) => {
  if (a && Object.prototype.hasOwnProperty.call(a, key.name)) {
    b[key.name] =
      databaseStage && key.transform ? key.transform(a[key.name]) : a[key.name]
    delete a[key.name]
  }
}

const resolver = (find, args) => {
  const options = {}
  supportedOptions.forEach((option) =>
    transformObjects(args, options, option, false)
  )

  return {
    args: {
      _id: { type: GraphQLString },
      ...args,
      ...options
    },
    resolve: (source, args) => {
      const resolveOptions = {}
      supportedOptions.forEach((option) =>
        transformObjects(args, resolveOptions, option, true)
      )
      return promisify(find)(args || {}, resolveOptions)
    }
  }
}

module.exports = resolver
