module.exports = createSearchEndpoint
const parseQueryString = require('@clocklimited/cf-crud-service-api-builder/parse-query-string')
const createFilterParser = require('@clocklimited/cf-crud-service-api-builder/filter-parser')

function createSearchEndpoint(
  service,
  url,
  router,
  logger,
  middleware,
  config
) {
  const conf = { afterHook: (f) => f, beforeHook: (f) => f, ...config }
  router.get(url + '/:id', middleware, function (req, res) {
    service.read(req.params.id, function (error, entity) {
      if (error) {
        logger.error(error)
        return res.status(400).json(error)
      }
      if (!entity) return res.status(404).json({ status: 'Not found' })
      res.json(conf.afterHook(entity))
    })
  })

  router.get(url, middleware, parseQueryString, function (req, res) {
    logger.debug('GET received (Search)', JSON.stringify(req.query))
    const { sort, filter, pagination } = req.query

    const isScoreSort = sort && sort[0][0] === 'score'
    const hasKeywords = filter && filter.keywords && filter.keywords !== ''

    const sortProperty =
      isScoreSort && hasKeywords ? { score: { $meta: 'textScore' } } : sort
    const options = { sort: sortProperty, projection: req.query.projection }
    const parseFilter = createFilterParser(service.schema)

    if (req.query.pagination.projection) {
      options.fields = req.query.pagination.projection
    }

    if (!isNaN(pagination.pageSize)) {
      options.limit = pagination.pageSize
      if (!isNaN(pagination.page)) {
        options.skip = (pagination.page - 1) * pagination.pageSize
      }
    }

    req.query.filter = parseFilter(req.query.filter)

    req.query.filter = conf.beforeHook(req.query.filter)

    service.search(req.query.keywords, req.query.filter, options, function (
      error,
      data,
      count
    ) {
      if (error) {
        logger.error(error.stack)
        return res.json(error, 400)
      }
      res.json({
        results: data.map(conf.afterHook),
        page: req.query.pagination.page,
        pageSize: req.query.pagination.pageSize,
        totalItems: count
      })
    })
  })
}
