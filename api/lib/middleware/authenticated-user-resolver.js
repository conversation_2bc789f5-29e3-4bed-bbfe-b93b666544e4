module.exports = init
const extend = require('lodash.assign')

function init(serviceLocator, options) {
  var defaultOptions = { reqProperty: 'authedClient' }

  options = extend({}, defaultOptions, options)

  function getAuthedUser(req, res, next) {
    serviceLocator.administratorService.findOne(
      { _id: req[options.reqProperty] },
      function (error, admin) {
        if (error) return next(error)
        req.authedUser = admin
        next()
      }
    )
  }

  return getAuthedUser
}
