module.exports = init
const extend = require('lodash.assign')

function init(serviceLocator, urlRoot, resource, actions) {
  if (!urlRoot || !resource)
    throw new Error('"urlRoot" and "resource" must be provided')

  const defaultActions = {}
  const acl = null

  defaultActions['GET:' + urlRoot] = 'read'
  defaultActions['GET:' + urlRoot + '/:id'] = 'read'
  defaultActions['POST:' + urlRoot] = 'create'
  defaultActions['PUT:' + urlRoot + '/:id?'] = 'update'
  defaultActions['PATCH:' + urlRoot + '/:id'] = 'update'
  defaultActions['DELETE:' + urlRoot + '/:id'] = 'delete'

  actions = extend({}, defaultActions, actions)

  function authorisation(req, res, next) {
    function getAcl(cb) {
      if (acl) return cb(acl)

      serviceLocator.roleService.loadAcl(serviceLocator.acl, function (
        error,
        acl
      ) {
        if (error) serviceLocator.logger.error(error)
        serviceLocator.logger.debug('Loading acl....')
        cb(acl)
      })
    }

    if (!req.authedUser) {
      return next(
        new Error(
          'Expecting an req.authedUser to be set, ensure getAuthedUser middleware is used'
        )
      )
    }

    getAcl(function (acl) {
      const action = actions[req.method + ':' + req.route.path]
      let message = null

      serviceLocator.logger.info(
        'Checking acl ' +
          resource +
          ':' +
          action +
          ' for admin: ' +
          req.authedUser._id
      )

      if (!action) {
        message =
          'Unknown action for method: ' +
          req.method +
          ' path: ' +
          req.route.path
        serviceLocator.logger.warn(message)
        if (serviceLocator.env && serviceLocator.env === 'development')
          return next(new Error(message))
      }

      if (!acl.allowed(req.authedUser.roles, resource, action)) {
        serviceLocator.logger.info(
          'Not Authorised: ' +
            resource +
            ':' +
            action +
            ' for admin: ' +
            req.authedUser._id
        )
        return res.status(401).json({
          error: 'You are not authorised to ' + action + ' ' + resource
        })
      }

      return next()
    })
  }

  return authorisation
}
