module.exports = parseQueryString
const extend = require('lodash.assign')

function parseQueryString(req, res, next) {
  try {
    req.query.filter = parseQueryStringObject(req.query.filter, {})
    req.query.sort = parseSortOptions(req.query.sort)
    req.query.pagination = parseQueryStringObject(req.query.pagination, {
      page: 1,
      pageSize: 50
    })
    req.query.keywords = req.query.keywords || ''
  } catch (e) {
    return res.status(400).json(e.message)
  }
  next()
}

function parseQueryStringObject(parameter, defaultValue) {
  var result

  if (parameter) {
    result = JSON.parse(parameter)
    if (typeof result !== 'object') {
      throw new Error('Invalid parameter provided ' + result)
    }
  }

  if (Array.isArray(defaultValue)) {
    if (!Array.isArray(result)) return defaultValue
    return result
  } else {
    return extend({}, defaultValue, result)
  }
}

function parseSortOptions(parameter) {
  if (!parameter) return null

  const rawOptions = parseQueryStringObject(parameter)
  let sort = 'asc'

  if (!rawOptions || !rawOptions[0]) return null

  if (typeof rawOptions[1] !== 'undefined') {
    sort = rawOptions[1]
  }

  return [[rawOptions[0], sort]]
}
