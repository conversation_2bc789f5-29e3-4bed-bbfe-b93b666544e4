const join = require('path').join
const bootstrap = require('./bootstrap')
const createConfigury = require('@clocklimited/configury')
const configury = createConfigury(join(__dirname, '/../config.json'))
const configurySecrets = createConfigury(join(__dirname, '/../secrets.json'))
const secrets = configurySecrets(process.env.NODE_ENV)
const config = configury(process.env.NODE_ENV)
const serviceLocator = require('service-locator')()
const createServerCluster = require('express-server-cluster')
const env = process.env.NODE_ENV || 'development'
const inDevelopmentMode = env === 'development'
// Only have debug logging on development
const logLevel = process.env.LOG_LEVEL || (inDevelopmentMode ? 'debug' : 'info')
const mailer = require('../lib/mailer')
const Metrics = require('cf-metrics')
const packageJson = require('../package.json')
const UberCache = require('uber-cache')
const createLogger = require('@serby/logger')
const createSentryProcessor = require('@serby/logger-sentry-processor')

const processors = [
  {
    processor: createLogger.createStdOutProcessor(),
    level: logLevel
  }
]

// eslint-disable-next-line
console.info('Starting API')

if (!inDevelopmentMode) {
  // eslint-disable-next-line
  console.info('Starting API Sentry')
  const Sentry = require('@sentry/node')
  Sentry.init({
    dsn: secrets.sentry.api.dsn,
    captureUnhandledRejections: true,
    release: packageJson.version,
    environment: env
  })
  serviceLocator.register('sentry', Sentry)
  processors.push({
    processor: createSentryProcessor(Sentry),
    level: 'warn'
  })
  Sentry.configureScope((scope) => {
    scope.setTag('application', 'api')
  })
}

// eslint-disable-next-line
console.info('Registering Initial Services')
serviceLocator
  .register('name', 'api')
  .register('env', env)
  .register('config', config)
  .register('secrets', secrets)
  .register('logger', createLogger('api', { logLevel, processors }))
  .register('cache', new UberCache())
  .register('samlSessions', new UberCache())
  .register('mailer', mailer(serviceLocator.secrets.mailAuth))
  .register(
    'metrics',
    new Metrics(config.statsd.host, config.statsd.port, {
      client: config.client,
      platform: config.platform,
      application: 'api',
      environment: env,
      logger: serviceLocator.logger
    })
  )

serviceLocator.logger.info('Running Bootstrap from app.js')
bootstrap(serviceLocator, function (error) {
  if (error) throw error

  serviceLocator.logger.info('Preboot')
  serviceLocator.server.emit('preBoot')

  serviceLocator.server.on('started', function () {
    serviceLocator.logger.info('Server running: ' + config.apiUrl)
  })

  var options = {
    port: process.env.PORT || serviceLocator.config.apiPort,
    numProcesses:
      serviceLocator.config.apiNumProcesses || process.env.NF_CPU_RESOURCES
  }

  serviceLocator.logger.info(
    `Starting Cluster | processes: ${options.numProcesses} port: ${options.port}`
  )

  createServerCluster(serviceLocator.server, serviceLocator.logger, options)
})
