vcl 4.0;

import directors;
import std;
include "logic-std.vcl";

backend site-production {
  .host = "site-production";
  .port = "7000";
}

//backend darkroom-production {
//  .host = "darkroom-production";
//  .port = "17999";
//}

backend admin-production {
  .host = "admin-production";
  .port = "7001";
}

backend api-production {
  .host = "api-production";
  .port = "7002";
}

sub vcl_recv {
    if (req.http.host ~ "^www") {
      return(synth(301, "https://" + regsub(req.http.host, "^www\.(.*)$", "\1") + req.url));
    }

    set req.backend_hint = site-production;

    if (req.http.host ~ "api") {
      set req.backend_hint = api-production;
      return(pass);
    }

    if (req.url ~ "^/dr/") {
      set req.url = regsub(req.url, "^/dr/", "/");
      set req.http.host = "darkroom-production";
      set req.http.port = "17999";
    } 
    else if (req.http.host ~ "proxy.local.cms.bizclikmedia.net") {
      set req.http.Host = regsub(req.http.Host, ".proxy.local.cms.bizclikmedia.net", "");
    }
    else if (req.http.host ~ "production-darkroom\." || req.http.host == "assets.bizclikmedia.net") {
      set req.http.host = "darkroom-production";
      set req.http.port = "17999";
    } else if (req.http.host ~ "admin") {
        set req.backend_hint = admin-production;
    }
}

sub vcl_synth {
  if (req.http.X-Clock-Client == "bizclik") {

    // Unplanned
    if (resp.status == 503) {
      set resp.http.Content-Type = "text/html; charset=utf-8";
      synthetic(std.fileread("/etc/varnish/holdingpage/bizclik_site-unplanned.html"));
      return(deliver);
    }

    // Planned
    if (resp.status == 567) {
      set resp.status = 503;
      set resp.http.Content-Type = "text/html; charset=utf-8";
      synthetic(std.fileread("/etc/varnish/holdingpage/bizclik_site-planned.html"));
      return(deliver);
    }
  }
}
include "logic-default.vcl";
