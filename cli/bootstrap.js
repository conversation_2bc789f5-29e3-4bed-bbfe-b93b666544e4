const join = require('path').join
const componentLoader = require('component-loader')
const glob = require('glob')

// Use this for standard environment setup that is need on all entry points.
// This will often be where components are initialised
function bootstrap(serviceLocator, cb) {
  const componentGlobs = [join(__dirname, '/../components/service/**/init.js')]
  const componentPaths = [].concat.apply(
    [],
    componentGlobs.map(function (path) {
      return glob.sync(path)
    })
  )

  const components = componentPaths.map(function (p) {
    return require(p)
  })

  componentLoader(
    components,
    function (loadComponentFn) {
      return loadComponentFn.bind(null, serviceLocator)
    },
    cb
  )
}

module.exports = bootstrap
