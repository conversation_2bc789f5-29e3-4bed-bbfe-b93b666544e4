const join = require('path').join
const bootstrap = require('./bootstrap')
const serviceLocator = require('service-locator')()
const env = process.env.NODE_ENV || 'development'
const inDevelopmentMode = env === 'development'
// Only have debug logging on development
const logLevel = process.env.LOG_LEVEL || (inDevelopmentMode ? 'debug' : 'info')
const mailer = require('../lib/mailer')
const Metrics = require('cf-metrics')
const UberCache = require('uber-cache')
const createConfigury = require('@clocklimited/configury')
const config = createConfigury(join(__dirname, '/../config.json'))(
  process.env.NODE_ENV
)
const configurySecrets = createConfigury(join(__dirname, '/../secrets.json'))
const secrets = configurySecrets(process.env.NODE_ENV)
const createLogger = require('@serby/logger')
serviceLocator
  .register('env', env)
  .register('config', config)
  .register('secrets', secrets)
  .register('logger', createLogger('cli', { logLevel }))
  .register('cache', new UberCache())
  .register('mailer', mailer(secrets.mailAuth))
  .register(
    'metrics',
    new Metrics(config.statsd.host, config.statsd.port, {
      client: config.client,
      platform: config.platform,
      application: 'site',
      environment: config.env,
      logger: serviceLocator.logger
    })
  )

module.exports = () =>
  new Promise((resolve, reject) => {
    bootstrap(serviceLocator, (error) => {
      if (error) reject(error)
      resolve(serviceLocator)
    })
  })
