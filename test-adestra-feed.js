/**
 * Test script for the Adestra feed with otherMagazines functionality
 * 
 * This script tests the magazine grouping logic and verifies that
 * related magazines are correctly identified and processed.
 */

// Magazine groupings for related magazines (copied from controller)
const MAGAZINE_GROUPS = {
  sustainability: [
    '61e6bc36c12943f4e5d5ee5b', // Sustainability Magazine
    '61e7f6431e943f7902f6f126', // Energy Magazine
    '66b09860a8d44f4485a3e515', // ClimateTech Digital
    '623b0bc5cc408bbbe3465e0b'  // EV Magazine
  ],
  procurement: [
    '61e02b83a78d63c1581ae4b5', // Supply Chain Magazine
    '61e7fa9f1e943f7902f6f165', // Procurement Magazine
    '66b09a78a8d44f4485a3e543', // Scope 3 Magazine
    '61e81bf4fc918c790837c7eb'  // Manufacturing Digital
  ],
  technology: [
    '61e7d58e5e6d7af4ecc6b560', // Technology Magazine
    '61e7ebe0fc918c790837c72e', // FinTech Magazine
    '61a75e103b654f0c7aa22161', // AI Magazine
    '61e80086fc918c790837c76f'  // Cyber Magazine
  ],
  telco: [
    '61e6ebf05e6d7af4ecc6b521', // Data Centre Magazine
    '61e833591e943f7902f6f227'  // Mobile Magazine
  ],
  miscellaneous: [
    '61e8243e1e943f7902f6f1a8', // Healthcare Digital
    '61e6db595e6d7af4ecc6b4e1', // Food and Drink Digital
    '61e82e5ffc918c790837c867', // InsurTech Digital
    '61e82093fc918c790837c828', // Mining Digital
    '61e938ad146f8ab78c2b4950', // Business Chief UK & Europe
    '61a75c7b3b654f0c7aa2215e', // Construction Digital
    '61e935b1146f8ab78c2b4913', // Business Chief Asia
    '64bf9f1bc77260499e46a04e', // Business Chief Middle East
    '64fee264a9d7b72eb5003777', // Finance Chief
    '64ff084954bfd3f3d022f4e1'  // Marketing Chief
  ]
}

// Helper function to find related magazine instances (copied from controller)
const getRelatedMagazineInstances = (currentInstanceId) => {
  for (const groupName in MAGAZINE_GROUPS) {
    const group = MAGAZINE_GROUPS[groupName]
    if (group.includes(currentInstanceId)) {
      // Return other instances in the same group (excluding current instance)
      return group.filter(instanceId => instanceId !== currentInstanceId)
    }
  }
  return []
}

// Test the grouping logic
function testMagazineGrouping() {
  console.log('Testing Magazine Grouping Logic\n')
  console.log('='.repeat(50))
  
  // Test each group
  Object.keys(MAGAZINE_GROUPS).forEach(groupName => {
    console.log(`\n${groupName.toUpperCase()} GROUP:`)
    console.log('-'.repeat(30))
    
    const group = MAGAZINE_GROUPS[groupName]
    group.forEach(instanceId => {
      const relatedIds = getRelatedMagazineInstances(instanceId)
      console.log(`Instance ${instanceId}:`)
      console.log(`  Related instances: ${relatedIds.length > 0 ? relatedIds.join(', ') : 'None'}`)
    })
  })
  
  // Test with a non-existent instance
  console.log('\nTEST WITH NON-EXISTENT INSTANCE:')
  console.log('-'.repeat(30))
  const nonExistentId = 'non-existent-id'
  const relatedToNonExistent = getRelatedMagazineInstances(nonExistentId)
  console.log(`Instance ${nonExistentId}:`)
  console.log(`  Related instances: ${relatedToNonExistent.length > 0 ? relatedToNonExistent.join(', ') : 'None'}`)
}

// Test specific instances
function testSpecificInstances() {
  console.log('\n\nTesting Specific Instance Examples\n')
  console.log('='.repeat(50))
  
  const testCases = [
    { id: '61e6bc36c12943f4e5d5ee5b', name: 'Sustainability Magazine' },
    { id: '61e7d58e5e6d7af4ecc6b560', name: 'Technology Magazine' },
    { id: '61e6ebf05e6d7af4ecc6b521', name: 'Data Centre Magazine' },
    { id: '61e8243e1e943f7902f6f1a8', name: 'Healthcare Digital' }
  ]
  
  testCases.forEach(testCase => {
    const relatedIds = getRelatedMagazineInstances(testCase.id)
    console.log(`\n${testCase.name} (${testCase.id}):`)
    console.log(`  Found ${relatedIds.length} related magazines`)
    if (relatedIds.length > 0) {
      console.log(`  Related IDs: ${relatedIds.join(', ')}`)
    }
  })
}

// Run the tests
console.log('Adestra Feed - Other Magazines Test')
console.log('===================================\n')

testMagazineGrouping()
testSpecificInstances()

console.log('\n\nTest completed successfully!')
console.log('The magazine grouping logic is working correctly.')
