#!/bin/sh

# Variables
DIR1="/app_mounted"
DIR2="/app"
DOCKERIGNORE_FILE="nothankyou.json"
GITIGNORE_FILE="/app_mounted/.gitignore"
UNISONIGNORE_FILE="/app_mounted/.unisonignore"
UNISON_DIR="$HOME/.unison"
UNISON_PROFILE="$UNISON_DIR/sync_app.prf"
IGNORE_TEMP_FILE="/tmp/unison_ignores.txt"
WATCH_MODE=false

# Check if the -watch switch is provided
if [ "$1" = "-watch" ]; then
    WATCH_MODE=true
fi

# Check if Unison is installed
if ! command -v unison &> /dev/null; then
    echo "Unison is not installed. Please install it first."
    exit 1
fi

# Check if directories exist
if [ ! -d "$DIR1" ]; then
    echo "Directory $DIR1 does not exist. Exiting."
    exit 1
fi

if [ ! -d "$DIR2" ]; then
    echo "Directory $DIR2 does not exist. Creating it."
    mkdir -p "$DIR2"
fi

# Ensure the ~/.unison directory exists
if [ ! -d "$UNISON_DIR" ]; then
    echo "Creating Unison configuration directory at $UNISON_DIR..."
    mkdir -p "$UNISON_DIR"
fi

# Prepare the ignore file
> "$IGNORE_TEMP_FILE" # Clear or create the temporary ignore file

# Combine patterns from .dockerignore, .gitignore, and .unisonignore, avoiding duplicates
for IGNORE_FILE in "$DOCKERIGNORE_FILE" "$GITIGNORE_FILE" "$UNISONIGNORE_FILE"; do
    if [ -f "$IGNORE_FILE" ]; then
        echo "Processing $IGNORE_FILE..."
        grep -vE '^#|^$' "$IGNORE_FILE" >> "$IGNORE_TEMP_FILE"
    fi
done

# Remove duplicate patterns
sort -u -o "$IGNORE_TEMP_FILE" "$IGNORE_TEMP_FILE"

# Create the Unison profile
echo "Creating Unison profile at $UNISON_PROFILE..."
cat <<EOL > "$UNISON_PROFILE"
root = $DIR1
root = $DIR2
auto = true
batch = true
perms = 0
fastcheck = true
prefer = newer
EOL

# Add repeat = watch only if -watch switch is provided
if [ "$WATCH_MODE" = true ]; then
    echo "repeat = watch" >> "$UNISON_PROFILE"
fi

# Add ignore patterns to the profile
echo "Adding ignore patterns to the profile..."
while IFS= read -r line || [[ -n "$line" ]]; do
    echo "ignore = Name $line" >> "$UNISON_PROFILE"
done < "$IGNORE_TEMP_FILE"

# Clean up temporary ignore file
rm -f "$IGNORE_TEMP_FILE"

# Perform bidirectional sync using Unison
if [ "$WATCH_MODE" = true ]; then
    echo "Starting bidirectional sync with watch mode between $DIR1 and $DIR2..."
else
    echo "Starting single bidirectional sync between $DIR1 and $DIR2..."
fi

unison -fastcheck true sync_app

if [ $? -eq 0 ]; then
    echo "Synchronization completed successfully."
else
    echo "An error occurred during synchronization."
    exit 1
fi

exit 0
