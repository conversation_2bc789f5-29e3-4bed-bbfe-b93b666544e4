services: 
  darkroom-production-database:
    image: mongo:5.0.21
    pull_policy: missing
    container_name: darkroom-production-database
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: darkroom
    ports:
      - "27018:27017"
    volumes:
      - darkroom-db-data:/data/db
    healthcheck:
      test: ["CMD", "mongo", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    networks:
      - bizclik

  darkroom-production:
    build:
      context: "https://github.com/clocklimited/Darkroom-api.git"
      dockerfile: Dockerfile
    image: darkroom:latest
    environment:
      NODE_ENV: development
      DATABASE_URI: *************************************************************************************
      SALT: 53370bcf28896ade6f6d810d528ef3d4
      KEY: 717d5fe4fcf607b3ee4f7287cf6cdfec
    ports:
      - "17999:17999"
    depends_on:
      darkroom-production-database:
        condition: service_healthy
    networks:
      - bizclik

volumes:
  darkroom-db-data:

networks:
  bizclik:
    name: bizclik
