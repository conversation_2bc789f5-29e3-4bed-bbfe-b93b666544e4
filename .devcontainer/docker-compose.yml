version: '3.9'
services:
  production-database:
    image: mongo:5.0.21
    pull_policy: missing
    container_name: production-database
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: production
    ports:
      - "27017:27017"
    volumes:
      - production-db-data:/data/db
    healthcheck:
      test: ["CMD", "mongo", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    networks:
      - bizclik

  app-builder:
    build:
      context: ..
      dockerfile: Dockerfile
      target: devcontainers
    image: bizclik-app:latest
    pull_policy: build
    command: ["sh", "-c", "/app_mounted/.devcontainer/build.sh"]
    environment:
      UNISONLOCALHOSTNAME: unison-container
    volumes:
      - app:/app
      - ../:/app_mounted
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
      - unison:/root/.unison
    networks:
      - bizclik
  
  unison-container: 
    build:
      context: ..
      dockerfile: Dockerfile
      target: devcontainers
    image: bizclik-app:latest
    pull_policy: build
    command: ["sh", "-c", "/app_mounted/.devcontainer/sync.sh -watch"]
    environment:
      UNISONLOCALHOSTNAME: unison-container
    volumes:
      - app:/app
      - ../:/app_mounted
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
      - unison:/root/.unison
    depends_on:
      app-builder:
        condition: service_completed_successfully
    networks:
      - bizclik

  admin-production:
    build:
      context: ..
      dockerfile: Dockerfile
      target: devcontainers
    image: bizclik-app:latest
    pull_policy: build
    command: ["sh", "-c", "./node_modules/.bin/npm-run-all --parallel dev:admin dev:admin-watch"]
    environment:
      NODE_ENV: development
      MONGO_SRV: ******************************************************************************
      MONGO_URL: ******************************************************************************
      DATABASE: production
      HOST: production-database
      USERNAME: admin
      PASSWORD: password
    ports:
      - "7001:7001"
    volumes:
      - app:/app
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
    depends_on:
      production-database:
        condition: service_healthy
      app-builder:
        condition: service_completed_successfully
      api-production:
        condition: service_healthy
    networks:
      - bizclik

  api-production:
    build:
      context: ..
      dockerfile: Dockerfile
      target: devcontainers
    image: bizclik-app:latest
    pull_policy: build
    command: ["sh", "-c", "npm run dev:api"]
    environment:
      NODE_ENV: development
      MONGO_SRV: ******************************************************************************
      MONGO_URL: ******************************************************************************
      DATABASE: production
      HOST: production-database
      USERNAME: admin
      PASSWORD: password
    ports:
      - "7002:7002"
    volumes:
      - app:/app
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
    depends_on:
      production-database:
        condition: service_healthy
      app-builder:
        condition: service_completed_successfully
    healthcheck:
      test: curl --fail http://localhost:7002/_health || exit 1
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 60s
    networks:
      - bizclik

  site-production:
    build:
      context: ..
      dockerfile: Dockerfile
      target: devcontainers
    image: bizclik-app:latest
    pull_policy: build
    command: ["sh", "-c", "npm run dev:site"]
    environment:
      NODE_ENV: development
      MONGO_SRV: ******************************************************************************
      MONGO_URL: ******************************************************************************
      DATABASE: production
      HOST: production-database
      USERNAME: admin
      PASSWORD: password
    ports:
      - "7000:7000"
    volumes:
      - app:/app
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
    depends_on:
      production-database:
        condition: service_healthy
      app-builder:
        condition: service_completed_successfully
      api-production:
        condition: service_healthy
    networks:
      - bizclik

  message-bus-production:
    build:
      context: ..
      dockerfile: Dockerfile
      target: devcontainers
    image: bizclik-app:latest
    pull_policy: build
    command: ["sh", "-c", "npm run dev:message-bus"]
    environment:
      NODE_ENV: development
      MONGO_SRV: ******************************************************************************
      MONGO_URL: ******************************************************************************
      DATABASE: production
      HOST: production-database
      USERNAME: admin
      PASSWORD: password
    volumes:
      - app:/app
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
    depends_on:
      production-database:
        condition: service_healthy
      app-builder:
        condition: service_completed_successfully
      api-production:
        condition: service_healthy
    networks:
      - bizclik

  worker-production:
    build:
      context: ..
      dockerfile: Dockerfile
      target: devcontainers
    image: bizclik-app:latest
    pull_policy: build
    command: ["sh", "-c", "npm run worker"]
    environment:
      NODE_ENV: development
      MONGO_SRV: ******************************************************************************
      MONGO_URL: ******************************************************************************
      DATABASE: production
      HOST: production-database
      USERNAME: admin
      PASSWORD: password
    ports:
      - "8114:8114"
    volumes:
      - app:/app
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
    depends_on:
      production-database:
        condition: service_healthy
      app-builder:
        condition: service_completed_successfully
      api-production:
        condition: service_healthy
    networks:
      - bizclik

  varnish-production:
    build:
      context: ../infra/varnish
      dockerfile: Dockerfile
      args:
        NODE_ENV: development
    image: varnish:latest
    pull_policy: build
    environment:
      NODE_ENV: development
      VARNISH_SIZE: 100M
      VARNISH_HTTP_PORT: 8080
    ports:
      - "8080:8080"
      - "80:8080"
    depends_on:
      - site-production
      - api-production
      - admin-production
    networks:
      - bizclik

volumes:
  production-db-data:
  app-dist:
  app:
  app-node_modules:
  unison:

networks:
  bizclik:
    name: bizclik
