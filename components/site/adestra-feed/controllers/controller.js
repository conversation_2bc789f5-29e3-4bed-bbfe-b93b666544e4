/**
 * Adestra Feed Controller
 *
 * This controller creates an RSS feed specifically formatted for Adestra email marketing platform.
 * It retrieves articles from the database based on the current instance and account context,
 * and formats them into a simplified RSS XML format that Adestra can consume.
 *
 * Local Environment Context:
 * - The feed is accessible at /api/adestra-feed
 * - It uses the instance and account objects from the request (populated by middleware)
 * - It queries the articleService and authorService from the serviceLocator
 * - Images are served from the darkroom service (configured in config.json)
 * - The feed is instance-specific (filtered by req.instance._id)
 */

// Dependencies
const { promisify } = require('util') // Converts callback-based methods to promises
const moment = require('moment') // Used for date calculations
const createImageUrlBuilder = require('cf-image-url-builder') // For image URL generation

// Custom utilities
const createInstanceFilteredServiceRetriever = require('../../../service/section/lib/instance-filtered-service-retriever')
const articleFilter = require('../../../service/article/filter')

// Import the plain text getter utility
const { getPlaintext } = require('../../../api/article/lib/plain-text-getter')

const IS_DEV_MODE = process.env.NODE_ENV === 'development'

// Magazine groupings for related magazines
const MAGAZINE_GROUPS = {
  sustainability: [
    '61e6bc36c12943f4e5d5ee5b', // Sustainability Magazine
    '61e7f6431e943f7902f6f126', // Energy Magazine
    '66b09860a8d44f4485a3e515', // ClimateTech Digital
    '623b0bc5cc408bbbe3465e0b'  // EV Magazine
  ],
  procurement: [
    '61e02b83a78d63c1581ae4b5', // Supply Chain Magazine
    '61e7fa9f1e943f7902f6f165', // Procurement Magazine
    '66b09a78a8d44f4485a3e543', // Scope 3 Magazine
    '61e81bf4fc918c790837c7eb'  // Manufacturing Digital
  ],
  technology: [
    '61e7d58e5e6d7af4ecc6b560', // Technology Magazine
    '61e7ebe0fc918c790837c72e', // FinTech Magazine
    '61a75e103b654f0c7aa22161', // AI Magazine
    '61e80086fc918c790837c76f'  // Cyber Magazine
  ],
  telco: [
    '61e6ebf05e6d7af4ecc6b521', // Data Centre Magazine
    '61e833591e943f7902f6f227'  // Mobile Magazine
  ],
  miscellaneous: [
    '61e8243e1e943f7902f6f1a8', // Healthcare Digital
    '61e6db595e6d7af4ecc6b4e1', // Food and Drink Digital
    '61e82e5ffc918c790837c867', // InsurTech Digital
    '61e82093fc918c790837c828', // Mining Digital
    '61e938ad146f8ab78c2b4950', // Business Chief UK & Europe
    '61a75c7b3b654f0c7aa2215e', // Construction Digital
    '61e935b1146f8ab78c2b4913', // Business Chief Asia
    '64bf9f1bc77260499e46a04e', // Business Chief Middle East
    '64fee264a9d7b72eb5003777', // Finance Chief
    '64ff084954bfd3f3d022f4e1'  // Marketing Chief
  ]
}

/**
 * Creates and configures the Adestra feed controller
 *
 * @param {Object} serviceLocator - Service locator containing all required services
 * @returns {Function} Controller setup function
 */
const createController = (serviceLocator) => {
  // Get logger for debugging
  const logger = serviceLocator.logger || console

  // Creates a method that respects instance and account filtering logic
  const retrieveInstanceFilteredServices = createInstanceFilteredServiceRetriever(
    serviceLocator
  )

  const cachedSections = {}

  // Helper function to find related magazine instances
  const getRelatedMagazineInstances = (currentInstanceId) => {
    for (const groupName in MAGAZINE_GROUPS) {
      const group = MAGAZINE_GROUPS[groupName]
      if (group.includes(currentInstanceId)) {
        // Return other instances in the same group (excluding current instance)
        return group.filter(instanceId => instanceId !== currentInstanceId)
      }
    }
    return []
  }

  // Helper function to get magazine issues for multiple instances
  const getOtherMagazineIssues = async (instanceIds) => {
    if (instanceIds.length === 0) return []

    try {
      const magazineIssues = await promisify(
        serviceLocator.magazineIssueService.find
      )(
        { instance: { $in: instanceIds } },
        {
          sort: { issueDate: -1 },
          projection: { title: 1, slug: 1, issueDate: 1, images: 1, instance: 1 }
        }
      )

      // Group by instance and get the latest issue for each
      const latestIssuesByInstance = {}
      magazineIssues.forEach(issue => {
        const instanceId = issue.instance.toString()
        if (!latestIssuesByInstance[instanceId] ||
            new Date(issue.issueDate) > new Date(latestIssuesByInstance[instanceId].issueDate)) {
          latestIssuesByInstance[instanceId] = issue
        }
      })

      return Object.values(latestIssuesByInstance)
    } catch (error) {
      logger.error('Error fetching other magazine issues:', error)
      return []
    }
  }

  async function buildArticleUrl(article, baseUrl) {
    const sectionId = article.sections?.[0]
    if (!sectionId) {
      return null
    }

    const section = await getCachedSection(sectionId)
    if (!section) {
      return null
    }

    const url = new URL(section.fullUrlPath, baseUrl)
    url.pathname = `${url.pathname}/${article.slug}`
    url.protocol = IS_DEV_MODE ? 'http:' : 'https:'

    return url
  }

  async function getCachedSection(sectionId) {
    if (sectionId in cachedSections) {
      return cachedSections[sectionId]
    }

    try {
      const results = await promisify(serviceLocator.sectionService.find)({
        _id: sectionId
      })

      const section = results?.[0] || null
      if (section) {
        cachedSections[sectionId] = section
      }

      return section
    } catch (error) {
      console.error('Failed to fetch section:', error)
      return null
    }
  }

  // Helper function to find the optimal image for an article
  const findOptimalImage = (article) => {
    if (!article.images) return ''

    // Target specifically the 668-wide image
    // First priority: hero_landscape_668 (exactly what we want)
    if (
      article.images.hero_landscape_668?.length > 0 &&
      article.images.hero_landscape_668[0]?.url
    ) {
      return article.images.hero_landscape_668[0].url
    }

    // Second priority: any key with "668" and "landscape"
    for (const key in article.images) {
      if (
        key.includes('668') &&
        key.includes('landscape') &&
        article.images[key]?.length > 0 &&
        article.images[key][0]?.url
      ) {
        return article.images[key][0].url
      }
    }

    // Third priority: any key with "668" (maintaining width)
    for (const key in article.images) {
      if (
        key.includes('668') &&
        article.images[key]?.length > 0 &&
        article.images[key][0]?.url
      ) {
        return article.images[key][0].url
      }
    }

    // Fallback to other images if no 668-wide image is found
    // Fourth priority: any landscape image
    for (const key in article.images) {
      if (
        key.includes('landscape') &&
        article.images[key]?.length > 0 &&
        article.images[key][0]?.url
      ) {
        return article.images[key][0].url
      }
    }

    // Last resort: any image
    for (const key in article.images) {
      if (article.images[key]?.length > 0 && article.images[key][0]?.url) {
        return article.images[key][0].url
      }
    }

    return ''
  }

  // Helper function to process image URL for email compatibility
  const processImageUrl = (url) => {
    if (!url) return ''

    // Always ensure we have a .jpg extension
    if (url.includes('.webp')) {
      url = url.replace('.webp', '.jpg')
    } else if (!url.endsWith('.jpg')) {
      // If URL doesn't already end with .jpg, add it
      url = `${url}.jpg`
    }

    return url
  }

  /**
   * Route: /api/adestra-feed
   *
   * Query parameters:
   * - limit: Maximum number of articles to include (default: 10)
   * - days: Number of days to look back for articles (default: 30)
   * - contentType: Type of content to include (currently only 'article' is supported)
   *
   * Response:
   * - Content-Type: application/rss+xml
   * - RSS 2.0 format XML with <item> entries for each article
   */
  serviceLocator.router.get('/api/adestra-feed', async (req, res) => {
    try {
      // Log request for debugging
      logger.info(
        `Adestra feed requested for instance: ${req.instance.name} (${req.instance._id})`
      )

      const config = req.query
      logger.debug('Adestra feed config:', config)

      // Get article filter specific to this instance/account context
      const { applyArticleFilter } = await retrieveInstanceFilteredServices(
        req.instance._id,
        req.account._id
      )

      // Build the Mongo query for articles published in the past X days (default: 30)
      const days = parseInt(config.days || '30')
      const articleQuery = {
        ...articleFilter.publicQuery(applyArticleFilter({})),
        displayDate: {
          $gte: moment().subtract(days, 'days').toDate()
        }
      }

      // Add content type filter if specified
      if (config.contentType) {
        articleQuery.contentType = config.contentType
      }

      const articleOptions = {
        sort: { displayDate: -1 },
        limit: parseInt(config.limit || '50')
      }

      logger.debug('Adestra feed article query:', articleQuery)

      // Set projection to include the full URL path
      const articleProjection = {
        headline: 1,
        sell: 1,
        displayDate: 1,
        __fullUrlPath: 1,
        images: 1,
        author: 1,
        legacyAuthorName: 1,
        featured: 1,
        body: 1,
        contentType: 1, // Add content type
        slug: 1, // Add slug
        sections: 1
      }

      // Query the articles from the service
      const articles = await promisify(
        serviceLocator.articleService.findPublic
      )(articleQuery, { ...articleOptions, projection: articleProjection })

      logger.debug(`Found ${articles.length} articles for Adestra feed`)

      // Verify each article has a valid URL path
      articles.forEach((article) => {
        if (!article.__fullUrlPath) {
          logger.warn(
            `Article ${article._id} (${article.headline}) missing __fullUrlPath`
          )
        }
      })

      // Query the current magazine issue
      const currentMagazine = await promisify(
        serviceLocator.magazineIssueService.findOne
      )(
        { instance: req.instance._id },
        { sort: { issueDate: -1 } }
      )

      logger.debug(
        `Found current magazine: ${
          currentMagazine ? currentMagazine.title : 'None'
        }`
      )

      // Query related magazine issues for other magazines in the same group
      const relatedInstanceIds = getRelatedMagazineInstances(req.instance._id)
      const otherMagazineIssues = await getOtherMagazineIssues(relatedInstanceIds)

      logger.debug(
        `Found ${otherMagazineIssues.length} other magazine issues for related instances`
      )

      // Query the authors (for populating <author> field)
      const authors = await promisify(serviceLocator.authorService.find)(
        { $or: [{ instances: req.instance._id }, { instances: [] }] },
        { projection: { name: 1 } }
      )

      // Construct the base URL using the same logic as the article service
      const protocol = IS_DEV_MODE ? 'http://' : 'https://'
      const subdomain = req.instance.subdomain
      const baseUrl = protocol + subdomain

      // Find the newest featured article (articles are already sorted by displayDate in descending order)
      const newestFeaturedArticle = articles.find(
        (article) => article.featured === true
      )
      const newestFeaturedArticleId = newestFeaturedArticle
        ? newestFeaturedArticle._id.toString()
        : null

      // Initialize arrays to hold regular, featured items, and current magazine
      const regularItems = []
      let featuredItem = null
      let currentMagazineItem = null

      // Helper function to get truncated body text
      const getTruncatedBodyText = (article) => {
        if (!article.body || !article.body.widgets) return ''

        // Get plain text from article body
        let fullText = getPlaintext(article.body.widgets)

        // Remove hyperlinks in square brackets
        fullText = fullText.replace(/\[[^\]]*\]/g, '')

        // If text is empty after cleaning, return empty string
        if (!fullText.trim()) return ''

        // Find the nearest period to the 500-character mark or end of text
        const targetLength = Math.min(500, fullText.length)

        // Look for periods within a reasonable range before and after target
        const lowerBound = Math.max(0, targetLength - 100)
        const upperBound = Math.min(fullText.length, targetLength + 100)

        let bestCutoffIndex = targetLength // Default if no period found
        let minDistance = 100 // Initialize with maximum possible distance

        // Check each character in our search range
        for (let i = lowerBound; i < upperBound; i++) {
          if (fullText[i] === '.') {
            const distance = Math.abs(i - targetLength)
            if (distance < minDistance) {
              minDistance = distance
              bestCutoffIndex = i
            }
          }
        }

        // Include the period in the truncated text
        bestCutoffIndex++

        // Ensure we don't go beyond the text length
        bestCutoffIndex = Math.min(bestCutoffIndex, fullText.length)

        return fullText.substring(0, bestCutoffIndex).trim()
      }

      // Iterate through each article and build individual <item> blocks
      for (const article of articles) {
        const author = authors.find((a) => a._id === article.author)

        // Properly escape title to handle special characters like &
        const title = (article.headline || 'Untitled')
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')

        const description = (article.sell || '')
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')

        const pubDate = new Date(article.displayDate).toUTCString()

        const link = await buildArticleUrl(article, baseUrl)

        // Create guid tag using the link as permalink
        const guidTag = `<guid isPermaLink="true">${link}</guid>`

        // Use the image URL builder like other parts of the codebase
        let imageUrl = ''
        if (article.images && article.images.widgets) {
          const urlBuilder = createImageUrlBuilder(
            serviceLocator.config.darkroom.url,
            serviceLocator.config.darkroom.salt,
            article.images.widgets
          )
          imageUrl = urlBuilder
            .getImage('Hero')
            .crop('Landscape')
            .constrain(668)
            .url()

          // Convert to JPG if needed
          if (imageUrl.includes('.webp')) {
            imageUrl = imageUrl.replace('.webp', '.jpg')
          } else if (!imageUrl.endsWith('.jpg')) {
            imageUrl = `${imageUrl}.jpg`
          }
        } else {
          // Fallback to your existing method
          imageUrl = processImageUrl(findOptimalImage(article))
        }

        // Create image tag if an image was found
        const imageTag = imageUrl
          ? `<enclosure url="${imageUrl}" length="0" type="image/jpg" />`
          : ''

        // Format author name
        const authorNameRaw =
          author?.name || article.legacyAuthorName || 'Unknown'
        const authorName = authorNameRaw
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')

        // Check if this is the newest featured article
        const isNewestFeatured =
          newestFeaturedArticleId &&
          article._id.toString() === newestFeaturedArticleId

        // Get truncated body text
        const bodyText = getTruncatedBodyText(article)
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')

        // Add body text tag
        const bodyTextTag = bodyText ? `<content>${bodyText}</content>` : ''

        if (isNewestFeatured) {
          // If this is the newest featured article, store it separately
          featuredItem = `
            <item>
              <featured>
                <title>${title}</title>
                <link>${link}</link>
                <description>${description}</description>
                <pubDate>${pubDate}</pubDate>
                <author>${authorName}</author>
                ${guidTag}
                ${imageTag}
                ${bodyTextTag}
              </featured>
            </item>`
        } else {
          // If not the newest featured article, add to regular items
          regularItems.push(`
            <item>
              <article>
                <title>${title}</title>
                <link>${link}</link>
                <description>${description}</description>
                <pubDate>${pubDate}</pubDate>
                <author>${authorName}</author>
                ${guidTag}
                ${imageTag}
                ${bodyTextTag}
              </article>
            </item>`)
        }
      }

      // Create current magazine item if a magazine was found
      if (currentMagazine) {
        // Format magazine name (title)
        const magazineName = (currentMagazine.title || 'Current Issue')
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')

        // Format magazine release date
        const magazineDate = new Date(currentMagazine.issueDate).toUTCString()

        // Build magazine URL
        const magazineLink = `${baseUrl}/magazine/${currentMagazine.slug}`

        // Get magazine cover image URL
        let magazineCoverUrl = ''
        if (currentMagazine.images && currentMagazine.images.widgets) {
          const urlBuilder = createImageUrlBuilder(
            serviceLocator.config.darkroom.url,
            serviceLocator.config.darkroom.salt,
            currentMagazine.images.widgets
          )
          magazineCoverUrl = urlBuilder
            .getImage('Cover')
            .crop()
            .url()

          // Convert to JPG if needed for email compatibility
          if (magazineCoverUrl.includes('.webp')) {
            magazineCoverUrl = magazineCoverUrl.replace('.webp', '.jpg')
          } else if (!magazineCoverUrl.endsWith('.jpg') && !magazineCoverUrl.endsWith('.png')) {
            magazineCoverUrl = `${magazineCoverUrl}.jpg`
          }
        }

        // Create enclosure tag for magazine cover
        const magazineCoverTag = magazineCoverUrl
          ? `<enclosure url="${magazineCoverUrl}" length="0" type="image/jpg" />`
          : ''

        currentMagazineItem = `
          <item>
            <currentMagazine>
              <name>${magazineName}</name>
              <date>${magazineDate}</date>
              <link>${magazineLink}</link>
              ${magazineCoverTag}
            </currentMagazine>
          </item>`
      }

      // Create other magazines item if related magazines were found
      let otherMagazinesItem = null
      if (otherMagazineIssues.length > 0) {
        // We need to get instance information for the other magazines to build proper URLs
        const otherInstanceIds = otherMagazineIssues.map(issue => issue.instance.toString())
        const otherInstances = await promisify(serviceLocator.instanceService.find)(
          { _id: { $in: otherInstanceIds } },
          { projection: { _id: 1, name: 1, subdomain: 1 } }
        )

        // Create a map for quick lookup
        const instanceMap = {}
        otherInstances.forEach(instance => {
          instanceMap[instance._id.toString()] = instance
        })

        // Build the other magazines XML
        const otherMagazineItems = otherMagazineIssues.map(issue => {
          const instance = instanceMap[issue.instance.toString()]
          if (!instance) return ''

          // Format magazine name (title)
          const magazineName = (issue.title || 'Current Issue')
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')

          // Format magazine release date
          const magazineDate = new Date(issue.issueDate).toUTCString()

          // Build magazine URL using the instance's subdomain
          const instanceProtocol = IS_DEV_MODE ? 'http://' : 'https://'
          const instanceBaseUrl = instanceProtocol + instance.subdomain
          const magazineLink = `${instanceBaseUrl}/magazine/${issue.slug}`

          // Get magazine cover image URL
          let magazineCoverUrl = ''
          if (issue.images && issue.images.widgets) {
            const urlBuilder = createImageUrlBuilder(
              serviceLocator.config.darkroom.url,
              serviceLocator.config.darkroom.salt,
              issue.images.widgets
            )
            magazineCoverUrl = urlBuilder
              .getImage('Cover')
              .crop()
              .url()

            // Convert to JPG if needed for email compatibility
            if (magazineCoverUrl.includes('.webp')) {
              magazineCoverUrl = magazineCoverUrl.replace('.webp', '.jpg')
            } else if (!magazineCoverUrl.endsWith('.jpg') && !magazineCoverUrl.endsWith('.png')) {
              magazineCoverUrl = `${magazineCoverUrl}.jpg`
            }
          }

          // Create enclosure tag for magazine cover
          const magazineCoverTag = magazineCoverUrl
            ? `<enclosure url="${magazineCoverUrl}" length="0" type="image/jpg" />`
            : ''

          return `
                <magazine>
                  <name>${magazineName}</name>
                  <date>${magazineDate}</date>
                  <link>${magazineLink}</link>
                  ${magazineCoverTag}
                </magazine>`
        }).filter(item => item !== '').join('')

        if (otherMagazineItems) {
          otherMagazinesItem = `
            <item>
              <otherMagazines>${otherMagazineItems}
              </otherMagazines>
            </item>`
        }
      }

      // Combine current magazine item first, then other magazines, then featured item, then regular items
      let rssItems = ''
      if (currentMagazineItem) {
        rssItems = currentMagazineItem
      }
      if (otherMagazinesItem) {
        rssItems += otherMagazinesItem
      }
      if (featuredItem) {
        rssItems += featuredItem
      }
      rssItems += regularItems.join('')

      // Create standard RSS 2.0 feed (no namespaces, simple structure, lowercase tags)
      const rssXml = `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0">
<channel>
  <title>${req.instance.name} – Articles</title>
  <link>${baseUrl}</link>
  <description>${req.instance.strapline
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')}</description>
  <language>en-us</language>
  <lastbuilddate>${new Date().toUTCString()}</lastbuilddate>
  <ttl>60</ttl>
  ${rssItems}
</channel>
</rss>`

      // Estimate feed size for logging
      const feedSizeBytes = Buffer.byteLength(rssXml, 'utf8')
      const feedSizeMB = (feedSizeBytes / (1024 * 1024)).toFixed(2)

      // Check if feed is within size limits
      const isWithinSizeLimit = feedSizeBytes < 10 * 1024 * 1024 // 10MB limit

      // Response headers for RSS and caching
      res.setHeader('Content-Type', 'application/rss+xml')
      res.setHeader('Cache-Control', 'public, max-age=600') // Allow caching for 10 minutes

      // Add additional headers to help with debugging
      res.setHeader('X-Feed-Size', `${feedSizeMB}MB`)
      res.setHeader('X-Feed-Articles', articles.length)
      res.setHeader('X-Feed-Generated', new Date().toISOString())

      // Send the feed
      res.send(rssXml.trim())

      // Log detailed information about the feed
      logger.info(
        `Adestra feed successfully generated with ${articles.length} articles, size: ${feedSizeMB}MB`
      )

      if (!isWithinSizeLimit) {
        logger.warn(
          `Adestra feed exceeds recommended size limit of 10MB. Current size: ${feedSizeMB}MB`
        )
      }

      // Log the feed URL for easy testing
      logger.info(`Feed URL: ${baseUrl}/api/adestra-feed`)
    } catch (err) {
      // Catch unexpected errors and log for debugging
      logger.error('Adestra feed error:', err)

      // Send more detailed error response
      const errorMessage =
        process.env.NODE_ENV === 'development'
          ? `Error generating Adestra feed: ${err.message}`
          : 'Internal Server Error'

      res.status(500).send(errorMessage)
    }
  })

  return { router: serviceLocator.router }
}

module.exports = createController
