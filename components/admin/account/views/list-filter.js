const compileJade = require('browjadify-compile')
const View = require('ventnor')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/list-filter.jade'))

class ListFilterView extends View {
  constructor(...args) {
    super(...args)
    this.$el.addClass('list-filters')
    this.$el.on('submit', 'form', this.handleSubmit.bind(this))
    this.$el.on('click', 'input[type=reset]', this.handleClear.bind(this))
  }

  trigger(...args) {
    return this.emit(...args)
  }

  updateDisplay(params) {
    this.$el.find('form [name=keywords]').val(params.keywords)

    const backMap = {
      'dateCreated,asc': 'dateCreated',
      'dateCreated,desc': '-dateCreated',
      'score,desc': '-score'
    }

    if (Array.isArray(params.sort)) {
      this.$el
        .find(
          '[name=sort] option[value=' + backMap[params.sort.join(',')] + ']'
        )
        .attr('selected', true)
    }
  }

  handleSubmit(e) {
    if (e) e.preventDefault()

    const params = { filter: {}, sort: [] }
    const map = {
      dateCreated: ['dateCreated', 'asc'],
      '-dateCreated': ['dateCreated', 'desc'],
      '-score': ['score', 'desc']
    }
    const keywords = this.$el.find('form [name=keywords]').val()

    params.sort = map[this.$el.find('form [name=sort]').val()]

    if (keywords.length) params.keywords = keywords

    this.emit('filter', params)
  }

  handleClear(e) {
    e.preventDefault()
    this.$el.find('form [name=keywords]').val('')
    this.$el.find('form [name=sort]').val('-dateCreated')
    this.handleSubmit()
  }

  render() {
    this.$el.empty().append(template({}))
    this.$el.find('.js-tooltip-trigger').tooltip({ html: true })
    return this
  }
}

module.exports = ListFilterView
