const BaseFormView = require('../../base/views/form')
const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/form.jade'))
const instanceOptionTemplate = compileJade(
  join(__dirname, '/../../instance/templates/instance-option.jade')
)

class FormView extends BaseFormView {
  render() {
    // Render the template
    this.$el.append(
      template({
        title: this.isNew ? 'New Account' : 'Edit Account',
        data: this.model.toJSON()
      })
    )

    // Render the toolbar
    this.toolbar.render().$el.appendTo(this.$el.find('.js-toolbar-root'))

    const editor = window.ace.edit(this.$el.find('.js-html-editor')[0])

    editor.getSession().setMode('ace/mode/html')
    editor.setOptions({ theme: 'ace/theme/chrome', tabSize: 2 })
    editor.resize()

    editor.getSession().on('change', () => {
      this.model.set('footerHtml', editor.getSession().getValue())
    })

    this.$el.find('.js-selectize').each((i, el) => {
      $(el).selectize({
        delimiter: ',',
        createOnBlur: true,
        create: true,
        onInitialize: () =>
          el.selectize.on('change', () => {
            this.model.set(el.name, el.selectize.getValue())
          })
      })
    })

    this.populateInstances()

    return this
  }

  populateInstances() {
    const currentInstance = this.model.get('primaryInstance')
    this.serviceLocator.instanceService.find(
      '',
      {},
      ['name'],
      {},
      (err, res) => {
        if (err)
          return this.serviceLocator.logger.error(
            err,
            'Could not load instances'
          )
        this.$el.find('select[name=primaryInstance]').append(
          res.results.map((instance) => {
            return instanceOptionTemplate({
              value: instance._id,
              name: instance.name,
              selected: currentInstance === instance._id
            })
          })
        )
      }
    )
  }
}

module.exports = FormView
