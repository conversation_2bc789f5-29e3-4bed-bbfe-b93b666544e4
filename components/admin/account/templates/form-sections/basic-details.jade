.panel.panel-styled
  .panel-header
    h2 Basic Details

  .panel-content

    .form-row(id='field--name', data-field='name')
      label
        span.form-label-text Name
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='name', value=data.name, autofocus)
      .js-error
      .form-row-description.form-copy
        p Normally a company name.

    .form-row(id='field--domain', data-field='domain')
      label
        span.form-label-text Domain
        input.control.control--text.form-field(type='text', name='domain', value=data.domain)
      .js-error
      .form-row-description.form-copy
        p This will be the base domain of the site.
        p Leaving this blank will allow instances to have a full domain.

    .form-row(id='field--supportEmailAddress', data-field='supportEmailAddress')
      label
        span.form-label-text Support Email Address
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='supportEmailAddress', value=data.supportEmailAddress)
      .js-error
      .form-row-description.form-copy
        p Used for sending support emails, such as the forgotten password email.

    .form-row(id='field--primaryInstance', data-field='primaryInstance')
      label
        span.form-label-text Primary Instance
          abbr(title='This field is required') *
        select.control.control--choice.form-field.js-instance(name='primaryInstance', value=data.primaryInstance)
          option(value='') -- Select Primary Instance --
      .js-error
      .form-row-description.form-copy
        p Default instance to show if the slug is missing.
