.panel.panel-styled
  .panel-header
    h2 Social Media Accounts
  .panel-content

    .form-row(id='field--addThisId', data-field='addThisId')
      label
        span.form-label-text AddThis Profile ID
        input.control.control--text.form-field(type='text', name='addThisId', placeholder='XX-XXXXXXXXXXXXXXXX', value=data.addThisId)
      .js-error
      .form-row-description.form-copy
        p If you have an AddThis account, visit your 
          a(href='https://www.addthis.com/settings/publisher', target='_blank') AddThis settings page
          |  to find your Profile ID. (opens in a new window)

    hr

    .form-row(id='field--facebookId', data-field='facebookId')
      label
        span.form-label-text Facebook
        .form-field
          .control-addon
            .control-addon__affix.control-addon__affix--prefix.control-addon__affix--attached
              .label.label--xlarge.label--plain https://facebook.com/
            input.control.control--text.control-addon__base(type='text', name='facebookId', placeholder='example', value=data.facebookId)
      .js-error
      .form-row-description.form-copy
        p Go to your Facebook page and copy the string of letters and/or numbers after “facebook.com/” in the URL

    .form-row(id='field--twitterId', data-field='twitterId')
      label
        span.form-label-text Twitter
        .form-field
          .control-addon
            .control-addon__affix.control-addon__affix--prefix.control-addon__affix--attached
              .label.label--xlarge.label--plain https://twitter.com/
            input.control.control--text.control-addon__base(type='text', name='twitterId', placeholder='example', value=data.twitterId)
      .js-error
      .form-row-description.form-copy
        p Go to your Twitter page and copy the string of letters and/or numbers after “twitter.com/” in the URL

    .form-row(id='field--googlePlusId', data-field='googlePlusId')
      label
        span.form-label-text Google+
        .form-field
          .control-addon
            .control-addon__affix.control-addon__affix--prefix.control-addon__affix--attached
              .label.label--xlarge.label--plain https://plus.google.com/
            input.control.control--text.control-addon__base(type='text', name='googlePlusId', placeholder='+example', value=data.googlePlusId)
            .control-addon__affix.control-addon__affix--suffix.control-addon__affix--attached
              .label.label--xlarge.label--plain /posts
      .js-error
      .form-row-description.form-copy
        p Go to your Google+ profile and copy the string of letters and/or numbers before “/posts” in the URL
        p May appear in the format “+example” or “116899029375914044550”

    .form-row(id='field--pinterestId', data-field='pinterestId')
      label
        span.form-label-text Pinterest
        .form-field
          .control-addon
            .control-addon__affix.control-addon__affix--prefix.control-addon__affix--attached
              .label.label--xlarge.label--plain https://pinterest.com/
            input.control.control--text.control-addon__base(type='text', name='pinterestId', placeholder='example', value=data.pinterestId)
      .js-error
      .form-row-description.form-copy
        p Go to your Pinterest profile and copy the string of letters and/or numbers after “pinterest.com/” in the URL

    .form-row(id='field--instagramId', data-field='instagramId')
      label
        span.form-label-text Instagram
        .form-field
          .control-addon
            .control-addon__affix.control-addon__affix--prefix.control-addon__affix--attached
              .label.label--xlarge.label--plain https://instagram.com/
            input.control.control--text.control-addon__base(type='text', name='instagramId', placeholder='example', value=data.instagramId)
      .js-error
      .form-row-description.form-copy
        p Go to your Instagram page and copy the string of letters and/or numbers after “instagram.com/” in the URL
