.panel.panel-styled
  .panel-header
    h2 Custom HTML
  .panel-content

    .form-row.form-row-full-width(id='field--footerHtml', data-field='footerHtml')
      label
        span.vhidden.form-label-text Custom Footer HTML
        textarea.control.control--text.control--multiline.js-html-editor.form-field(name='footerHtml', rows='20')= data.footerHtml
      input.js-html-value(type='hidden', name='footerHtml', value=data.footerHtml)
      .js-error
      .form-row-description.form-copy
        p Use this to add custom HTML to the bottom of all pages on this instance
