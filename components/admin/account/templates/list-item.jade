.list-item
  .list-item-header

    .list-item-actions
      if allowed('account', 'delete')
        label.list-item-select
          input.js-select(type='checkbox')

      .btn-group
        a.btn.btn--small.dropdown-toggle(data-toggle='dropdown', href='#')
            span.caret
          ul.dropdown-menu.pull-right
            li
              a.js-show-revisions Show Revisions

    if allowed('account', 'update')
      h2
        a.js-edit(href='/accounts/#{data._id}/form')= data.name
    else
      h2= data.name

  .list-item-content
    dl
      dt Created:
      dd= format(data.dateCreated, 'calendar')
