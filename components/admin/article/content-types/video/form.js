const View = require('ventnor')
const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/form.jade'))
const AssetModel = require('../../../asset/models/asset')

class VideoForm extends View {
  constructor(serviceLocator, model, articleFormAddImages) {
    super()
    this.model = model
    this.serviceLocator = serviceLocator
    this.articleFormAddImages = articleFormAddImages
    this.$el.on(
      'click',
      '.js-generate-thumbnail',
      this.generateThumbnail.bind(this)
    )
  }

  async generateThumbnail() {
    this.$el
      .find('.js-generate-thumbnail')
      .text('Please wait...')
      .attr('disabled', 'disabled')
    const videoProvider = this.$el.find('[name=videoProvider]').val()
    const videoId = this.$el.find('[name=videoId]').val()
    const accountId = this.serviceLocator.session.account

    this.serviceLocator.articleService.generateVideoThumbnail(
      { videoProvider, videoId, accountId: accountId },
      (error, response) => {
        if (error) {
          this.$el
            .find('.js-generate-thumbnail')
            .text('Generate thumbnail')
            .attr('disabled', false)
          return alert(error)
        }
        const collection = new window.Backbone.Collection()
        collection.add(new AssetModel(response))
        this.articleFormAddImages(collection)
        this.$el.find('.js-generate-thumbnail').text('Success!')
      }
    )
  }

  render() {
    this.$el.append(template({ article: this.model.toJSON() }))
    return this
  }
}

module.exports = VideoForm
