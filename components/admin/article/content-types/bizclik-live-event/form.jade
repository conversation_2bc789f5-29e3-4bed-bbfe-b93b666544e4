.panel.panel-styled(id='section-configuration')
  .panel-header
    div
      h2 Events
      p These allow articles such as Speaker <PERSON> and Agenda Topics to be linked to events.

  .panel-content

    .form-row(id='field--eventId', data-field='eventId')
      span.form-label-text Event
        abbr(title='This field is required') *
      label
        .form-field.js-event-select
      .js-error

    .form-row(id='field--eventArticleCategoryKey', data-field='eventArticleCategoryKey' style="display: none;")
      label
        span.form-label-text Event Article Category
          abbr(title='This field is required') *
        label
          .form-field.js-event-article-category-select
        .js-error
