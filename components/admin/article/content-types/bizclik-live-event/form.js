const View = require('ventnor')
const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/form.jade'))
const EventSelect = require('../../../event/views/components/event-select')
const EventArticleCategorySelect = require('../../../event/views/components/event-article-category-select')

class EventForm extends View {
  constructor(serviceLocator, model) {
    super()
    this.model = model
    this.serviceLocator = serviceLocator
    this.setupEventControls()
  }

  setupEventControls() {
    const eventId = this.model.get('eventId')
    const eventCategoryKey = this.model.get('eventArticleCategoryKey')
    this.eventSelect = new EventSelect(this.serviceLocator, eventId)
    if (!this.serviceLocator.allowed('eventArticle', 'update'))
      this.eventSelect.el.disabled = true
    this.eventSelect.on('change', (value) => {
      this.model.set('eventId', value)
      if (!value) this.model.set('eventArticleCategoryKey', null)
      this.renderEventArticleCategorySelect()
    })
    this.eventArticleCategorySelect = new EventArticleCategorySelect(
      this.serviceLocator,
      eventId,
      eventCategoryKey
    )
    if (!this.serviceLocator.allowed('eventArticle', 'update'))
      this.eventArticleCategorySelect.el.disabled = true
  }

  renderEventSelect() {
    this.$el.find('.js-event-select').append(this.eventSelect.render().$el)
  }

  renderEventArticleCategorySelect() {
    const eventId = this.model.get('eventId')
    const eventArticleCategoryKey = this.model.get('eventArticleCategoryKey')
    if (eventId) {
      this.$el.find('[data-field=eventArticleCategoryKey]').show()
    } else {
      this.$el.find('[data-field=eventArticleCategoryKey]').hide()
    }
    this.eventArticleCategorySelect = new EventArticleCategorySelect(
      this.serviceLocator,
      eventId,
      eventArticleCategoryKey
    )
    if (!this.serviceLocator.allowed('eventArticle', 'update'))
      this.eventArticleCategorySelect.el.disabled = true
    this.eventArticleCategorySelect.on('change', (value) => {
      this.model.set('eventArticleCategoryKey', value)
    })
    this.$el
      .find('.js-event-article-category-select')
      .empty()
      .append(this.eventArticleCategorySelect.render().$el)
  }

  render() {
    this.$el.append(template({ article: this.model.toJSON() }))
    this.renderEventSelect()
    this.renderEventArticleCategorySelect()
    return this
  }
}

module.exports = EventForm
