.panel.panel-styled(id='section-configuration')
  .panel-header
    h2 Interview

  .panel-content

    .form-row(id='field--quote', data-field='quote')
      label
        span.form-label-text Quote
        input.control.control--text.form-field(type='text', name='quote', value=article.quote)
      .js-error

    .form-row(id='field--attribution', data-field='attribution')
      label
        span.form-label-text Attribution
        input.control.control--text.form-field(type='text', name='attribution', value=article.attribution)
      .js-error

    .form-row(id='field--subAttribution', data-field='subAttribution')
      label
        span.form-label-text Sub-Attribution
        input.control.control--text.form-field(type='text', name='subAttribution', value=article.subAttribution)
      .js-error

   