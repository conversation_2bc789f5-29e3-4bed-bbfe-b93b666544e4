const View = require('ventnor')
const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/form.jade'))
const dateTimePickers = require('../../../../../admin/source/js/lib/init-date-time-pickers')

class EventForm extends View {
  constructor(serviceLocator, model) {
    super()
    this.model = model
    this.initDateTimePickers = dateTimePickers(
      window.config.locale.longDateFormat.LLLL,
      model
    )
    this.on('remove', () => {
      this.dateTimePickers.forEach((picker) => picker.destroy())
    })
  }

  render() {
    this.$el.append(template({ article: this.model.toJSON() }))
    this.dateTimePickers = this.$el
      .find('.js-date-time-picker')
      .toArray()
      .map(this.initDateTimePickers)
    return this
  }
}

module.exports = EventForm
