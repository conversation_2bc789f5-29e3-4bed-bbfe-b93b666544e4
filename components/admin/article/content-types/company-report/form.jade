.panel.panel-styled(id='section-configuration')
  .panel-header
    h2 Company Report

  .panel-content

    .form-row(id='field--magazineOrigin', data-field='magazineOrigin')
      label
        span.form-label-text Magazine Origin
        select.control.control--choice.form-field(name='magazineOrigin')
          option(value='', selected=article.magazineOrigin === '') -- Select a Magazine Origin --
          option(value='issu', selected=article.magazineOrigin === 'issu') ISSU
          option(value='joomag', selected=article .magazineOrigin === 'joomag') JooMag

    .form-row(id='field--issuPublicationId', data-field='issuPublicationId')
      label
        span.form-label-text ISSU Publication ID
        input.control.control--text.form-field(type='text', name='issuPublicationId', value=article.issuPublicationId)
      .js-error

    .form-row(id='field--issuIssueId', data-field='issuIssueId')
      label
        span.form-label-text ISSU Issue ID
        input.control.control--text.form-field(type='text', name='issuIssueId', value=article.issuIssueId)
      .js-error
        p If the origin is #[strong Joomag] please include the name of the magazine (e.g. fintech-magazine-may-2023/0498198001685715369)
