const View = require('ventnor')
const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/form.jade'))

class InterviewForm extends View {
  constructor(serviceLocator, model) {
    super()
    this.model = model
  }

  render() {
    this.$el.append(template({ article: this.model.toJSON() }))
    return this
  }
}

module.exports = InterviewForm
