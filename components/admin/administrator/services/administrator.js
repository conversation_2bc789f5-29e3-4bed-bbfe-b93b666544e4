const CrudService = require('../../../../admin/source/js/lib/crud-service')

class AdministratorService extends CrudService {
  get name() {
    return 'AdministratorService'
  }

  get urlRoot() {
    return '/administrators'
  }

  reset2fa(model, cb) {
    this.partialUpdate(
      model.get('_id'),
      { twoFaKey: null, twoFaChallengeDates: {} },
      cb
    )
  }
}

module.exports = AdministratorService
