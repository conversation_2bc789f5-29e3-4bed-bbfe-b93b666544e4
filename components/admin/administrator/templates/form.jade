.page-content

  .js-toolbar-root

  .centering

    header.page-header

      h1= title

    form
      .js-errors-summary

      //- Allow submission of form using enter button
      input.hidden(type='submit')

      .panel.panel-styled(id='user-details')

        .panel-header
          h2 User Details

        .panel-content

          .form-row(id='field--firstName', data-field='firstName')
            label
              span.form-label-text First Name
                abbr(title='This field is required') *
              input.control.control--text.form-field(type='text', name='firstName', maxlength='200', value=data.firstName, autofocus)
            .js-error

          .form-row(id='field--lastName', data-field='lastName')
            label
              span.form-label-text Last Name
                abbr(title='This field is required') *
              input.control.control--text.form-field(type='text', name='lastName', maxlength='200', value=data.lastName)
            .js-error

          .form-row(id='field--emailAddress', data-field='emailAddress')
            label
              span.form-label-text Email Address
                abbr(title='This field is required') *
              input.control.control--text.form-field(type='text', name='emailAddress', maxlength='200', value=data.emailAddress)
            .js-error

          if twoFaEnabled  
            .form-row.form-row-boolean(id='field--twoFaEnabled', data-field='twoFaEnabled')
              label
                span.form-label-text Two Factor Authentication
                .form-field
                  input.control.control--boolean(type='checkbox', name='twoFaEnabled', checked=data.twoFaEnabled)
                  span Should this user require two factor authentication when logging in?
              .js-error

      .panel.panel-styled(id='access-level')

        .panel-header
          h2 Access Level

        .panel-content

          .form-row.js-account(id='field--account', data-field='account')
            label
              span.form-label-text Account
              .js-accounts.form-field
            .js-error
            .form-row-description.form-copy
              p Please leave blank if the user needs to access all accounts.

          .form-row(id='field--instances', data-field='instances')
            label
              span.form-label-text Instances
              .js-instances.form-field
            .js-error
            .form-row-description.form-copy
              p Please leave blank if the user needs to access all instances.

          .form-row.form-row-boolean(id='field--roles', data-field='roles')
            span.form-label-text Roles
            .form-field
              ul
                each role in roles
                  li
                    label
                      if typeof data.roles === 'undefined' || data.roles.indexOf(role.name) === -1
                        input.control.control--boolean(type='checkbox', name='roles', value=role.name)
                        span= role.name
                      else
                        input.control.control--boolean(type='checkbox', name='roles', value=role.name, checked='checked')
                        span= role.name
