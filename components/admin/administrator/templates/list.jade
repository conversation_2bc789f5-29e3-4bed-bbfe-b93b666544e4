.page-content

  .toolbar
    .centering.js-toolbar
      if (allowed(name.resource, 'delete'))
        .toolbar__left
          button.btn.js-delete(type='button') Delete Selected
      if (allowed(name.resource, 'create'))
        button.btn.btn--action.js-new(type='button') New #{name.singular}

  .centering

    header.page-header
      h1= name.plural

    .grid.grid--reverse
      .grid__item.one-quarter
        .js-filters

      .grid__item.three-quarters
        .list-container
          .js-controls

          .grid.list-grid.js-items
          .pagination
            p
              | Showing 
              b.js-item-count
              |  of 
              b.js-total-item-count
              |  items
            button.btn.js-more(type='button') Load more
