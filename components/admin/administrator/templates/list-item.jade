.list-item
  .list-item-header

    .list-item-actions
      if allowed('administrator', 'delete')
        label.list-item-select
          input.js-select(type='checkbox')
      if allowed('administrator', 'update')
        button.btn.btn--small(type='button').js-password-reset Reset Password
      .btn-group
        a.btn.btn--small.dropdown-toggle(data-toggle='dropdown', href='#')
            span.caret
          ul.dropdown-menu.pull-right
            if twoFaEnabled
              li
                a.js-2fa-reset Reset 2FA
            li
              a.js-show-revisions Show Revisions

    if allowed('administrator', 'update')
      h2
        a.js-edit(href='/administrators/#{data._id}/form') #{data.firstName} #{data.lastName}
    else
      h2 #{data.firstName} #{data.lastName}

  .list-item-content
    dl
      dt Email:
      dd= data.emailAddress
      if data.__instances.length != 0
        dt Instance:
        dd
          each instance in data.__instances
            span.instance-logo
              img(src=instance.logoUrl, alt=instance.name, title=instance.name)
