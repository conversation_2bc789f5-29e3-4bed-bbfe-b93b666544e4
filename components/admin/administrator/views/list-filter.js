const compileJade = require('browjadify-compile')
const View = require('ventnor')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/list-filter.jade'))

class ListFilterView extends View {
  constructor(...args) {
    super(...args)
    this.$el.addClass('list-filters')
    this.$el.on('submit', 'form', this.handleSubmit.bind(this))
    this.$el.on('click', 'input[type=reset]', this.handleClear.bind(this))
    this.$el.on('change', '[name=instance]', this.handleSubmit.bind(this))
  }

  trigger(...args) {
    return this.emit(...args)
  }

  updateDisplay(params) {
    this.$el.find('form [name=keywords]').val(params.keywords)
  }

  handleSubmit(e) {
    if (e) e.preventDefault()

    const params = { filter: {}, sort: [] }
    const keywords = this.$el.find('form [name=keywords]').val()

    if (keywords.length) params.keywords = keywords

    this.emit('filter', params)
  }

  handleClear(e) {
    e.preventDefault()
    this.$el.find('form [name=keywords]').val('')
    this.handleSubmit()
  }

  render() {
    this.$el.empty().append(template({ instances: this.instances }))
    this.$el.find('.js-tooltip-trigger').tooltip({ html: true })
    return this
  }
}

module.exports = ListFilterView
