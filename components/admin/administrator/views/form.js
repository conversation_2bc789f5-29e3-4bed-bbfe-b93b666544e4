const BaseFormView = require('../../base/views/form')
const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/form.jade'))
const AccountSelect = require('../../account/views/account-select')
const BaseSelect = require('../../base/views/select')

class FormView extends BaseFormView {
  constructor(...args) {
    super(...args)
    this.roles = this.model.get('adminRoles')
    this.accountSelectView = new AccountSelect(
      this.serviceLocator,
      this.model.get('account')
    )
    this.accountSelectView.notSetLabel = '-- All accounts --'
    this.instanceSelectView = new BaseSelect(
      this.serviceLocator,
      'instanceService',
      this.model.get('instances'),
      false,
      'Instance',
      'Instances',
      'name',
      'name'
    )
    this.instanceSelectView.$el.attr('name', 'instances')
  }

  render() {
    // Render the template
    this.$el.append(
      template({
        title: this.isNew ? 'New Administrator' : 'Edit Administrator',
        data: this.model.toJSON(),
        roles: this.roles,
        twoFaEnabled: this.serviceLocator.config.twoFa.enabled
      })
    )

    // Render the toolbar
    this.toolbar.render().$el.appendTo(this.$el.find('.js-toolbar-root'))

    this.accountSelectView
      .populate()
      .render()
      .$el.appendTo(this.$el.find('.js-accounts'))

    this.$el.find('.js-instances').append(this.instanceSelectView.render().$el)
    this.instanceSelectView.on('change', (instances) =>
      this.model.set('instances', instances)
    )
    this.attachView(this.instanceSelectView)

    return this
  }
}

module.exports = FormView
