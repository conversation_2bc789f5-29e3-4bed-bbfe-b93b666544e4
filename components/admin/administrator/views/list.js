const BaseListView = require('../../revision/views/revision-list-view')
const ListItemView = require('./list-item')
const FilterView = require('./list-filter')
const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/list.jade'))

class ListView extends BaseListView {
  get FilterView() {
    return FilterView
  }

  get ListItemView() {
    return ListItemView
  }

  get name() {
    return {
      singular: 'Administrator',
      plural: 'Administrators',
      resource: 'administrator'
    }
  }

  get template() {
    return template
  }

  addListItem(model) {
    const listItem = new this.ListItemView(this.serviceLocator, model)
    this.listenTo(listItem, 'edit', this.emit.bind(this, 'edit', model.id))
    this.listenTo(listItem, 'reset2fa', this.emit.bind(this, 'reset2fa', model))
    this.listenTo(
      listItem,
      'showRevisions',
      this.emit.bind(this, 'showRevisions', model)
    )
    this.attachView(listItem)
    this.$el.find('.js-items').append(listItem.render().$el)
  }
}

module.exports = ListView
