const BaseListItemView = require('../../base/views/list-item')
const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/list-item.jade'))
const modal = require('modal')
const createDarkroomUrlBuilder = require('@clocklimited/darkroom-url-builder')

class ListItemView extends BaseListItemView {
  constructor(...args) {
    super(...args)
    this.$el.on(
      'click',
      '.js-show-revisions',
      this.emit.bind(this, 'showRevisions')
    )
    this.$el.on('click', '.js-password-reset', this.confirmReset.bind(this))
    this.$el.on('click', '.js-2fa-reset', this.confirm2faReset.bind(this))
  }

  get template() {
    return template
  }

  confirm2faReset() {
    modal({
      title: 'Reset 2 Factor Verification',
      content:
        'This will reset the 2 factor verification for this user. ' +
        'Next time they log in the will be prompted to set up 2fa. Are you sure?',
      buttons: [
        { text: 'Cancel', event: 'cancel', className: 'btn' },
        { text: 'Reset', event: 'confirm', className: 'btn btn--success' }
      ]
    }).on('confirm', this.emit.bind(this, 'reset2fa'))
  }

  confirmReset() {
    modal({
      title: 'Reset Password',
      content:
        'This will send a password reset email to this administrator. Are you sure?',
      buttons: [
        { text: 'Cancel', event: 'cancel', className: 'btn' },
        { text: 'Send', event: 'confirm', className: 'btn btn--success' }
      ]
    }).on('confirm', this.sendResetEmail.bind(this))
  }

  sendResetEmail() {
    const email = this.model.get('emailAddress')

    $.ajax({
      type: 'POST',
      url: window.config.apiUrl + '/administrator/password-reset-request',
      data: JSON.stringify({
        emailAddress: email,
        returnUrl: window.config.adminUrl + '/password-reset'
      }),
      error: (error) => {
        const parseError = JSON.parse(error.responseText)
        if (parseError.errors) {
          this.showErrors(parseError.errors)
        } else {
          this.showErrors(parseError)
        }
      },
      dataType: 'json',
      contentType: 'application/json'
    })
  }

  render() {
    const data = this.model.toJSON()
    const drUrl = this.serviceLocator.config.darkroom.url
    const drSalt = this.serviceLocator.config.darkroom.salt

    data.__instances.forEach((instance, key, instances) => {
      if (instance && instance.logo) {
        const logoUrlBuilder = createDarkroomUrlBuilder(drUrl, drSalt)
        const url = logoUrlBuilder()
          .resource(instance.logo)
          .filename('preview.jpg')
          .url()

        instances[key].logoUrl = url
      }
    })

    this.$el.empty().append(
      this.template({
        data,
        allowed: this.serviceLocator.allowed,
        format: this.serviceLocator.format,
        index: this.index,
        totalItems: this.totalItems,
        twoFaEnabled: this.serviceLocator.config.twoFa.enabled
      })
    )
    this.$el.find('.js-tooltip-trigger').tooltip({ html: true })
    return this
  }
}

module.exports = ListItemView
