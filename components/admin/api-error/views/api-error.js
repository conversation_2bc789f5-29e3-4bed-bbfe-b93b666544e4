const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/api-error.jade'))

module.exports = window.Backbone.View.extend({
  initialize: function () {
    this.showing = false
    $('#api-error').remove()
  },
  showing: false,
  render: function () {
    if (!this.showing) {
      $('body').prepend(template())
      $('#api-error').animate(
        {
          height: '82px'
        },
        300,
        function () {
          $(this).find('.message').addClass('visible')
        }
      )
      this.showing = true
    }
    return this
  },
  remove: function () {
    var that = this

    $('#api-error').find('.message').removeClass('visible')
    $('#api-error').animate(
      {
        height: '0px'
      },
      1000,
      function () {
        $('#api-error').remove()
        that.showing = false
      }
    )
    return this
  }
})
