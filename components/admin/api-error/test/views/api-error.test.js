const mockdate = require('mockdate')
const assert = require('assert')
const testEnv = require('../../../../../admin/test/admin/test-env')

describe('api-error', function () {
  let ApiErrorView

  before(testEnv)

  before(function () {
    ApiErrorView = require('../../views/api-error')
  })

  after(function () {
    mockdate.reset()
  })

  it('should not be showing when first initiated', function () {
    var apiErrorView = new ApiErrorView()

    assert.strictEqual(apiErrorView.showing, false)
    assert.strictEqual($('#api-error').length, 0)
  })

  it('should be showing when rendered', function () {
    var apiErrorView = new ApiErrorView()

    apiErrorView.render()
    mockdate.set(new Date(Date.now() + 1005))

    assert.strictEqual(apiErrorView.showing, true)
    assert.strictEqual($('#api-error').length, 1)
  })
})
