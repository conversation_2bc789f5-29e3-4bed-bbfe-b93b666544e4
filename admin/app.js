const join = require('path').join
const serviceLocator = require('service-locator')()
const bootstrap = require('./bootstrap')
const createServerCluster = require('express-server-cluster')
const env = process.env.NODE_ENV || 'development'
const inDevelopmentMode = env === 'development'
const logLevel = process.env.LOG_LEVEL || (inDevelopmentMode ? 'debug' : 'info')
const createConfigury = require('@clocklimited/configury')
const configury = createConfigury(join(__dirname, '/../config.json'))
const configurySecrets = createConfigury(join(__dirname, '/../secrets.json'))
const config = configury(process.env.NODE_ENV)
const secrets = configurySecrets(process.env.NODE_ENV)
const packageJson = require('../package.json')
const createLogger = require('@serby/logger')
const createSentryProcessor = require('@serby/logger-sentry-processor')

config.apiUrl = process.env.API_URL || config.apiUrl
config.apiUrlInternal = process.env.API_URL_INTERNAL || config.apiUrlInternal

const processors = [
  {
    processor: createLogger.createStdOutProcessor(),
    level: logLevel
  }
]

if (!inDevelopmentMode) {
  const Sentry = require('@sentry/node')
  Sentry.init({
    dsn: secrets.sentry.admin.dsn,
    captureUnhandledRejections: true,
    release: packageJson.version,
    environment: env
  })
  serviceLocator.register('sentry', Sentry)
  processors.push({
    processor: createSentryProcessor(Sentry),
    level: 'warn'
  })
  Sentry.configureScope((scope) => {
    scope.setTag('application', 'admin')
  })
}

serviceLocator
  .register('env', env)
  .register('config', config)
  .register('secrets', secrets)
  .register('logger', createLogger('admin', { logLevel, processors }))

bootstrap(serviceLocator, function (error) {
  if (error) throw error

  serviceLocator.server.on('started', function () {
    serviceLocator.logger.info('Server running: ' + config.adminUrl)
  })

  var options = {
    port: serviceLocator.config.adminPort || process.env.PORT,
    numProcesses:
      serviceLocator.config.adminNumProcesses || process.env.NF_CPU_RESOURCES
  }
  createServerCluster(serviceLocator.server, serviceLocator.logger, options)
})
