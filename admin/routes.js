module.exports = createRoutes

const request = require('request')
const createHealthCheckRoute = require('./lib/health-check')
const pick = require('./lib/path-picker')
const configWhiteList = require('./config-whitelist.json')

function createRoutes(serviceLocator) {
  function renderApp(req, res) {
    res.render('app', {
      config: serviceLocator.config,
      environment: serviceLocator.env
    })
  }

  function ensureSetup(req, res, next) {
    var statusUrl = serviceLocator.config.apiUrlInternal + '/status'
    serviceLocator.logger.info('Making status request to API', statusUrl)

    request(
      statusUrl,
      { json: true, headers: { accept: 'application/json' }, strictSSL: false },
      function (error, response) {
        if (error) {
          serviceLocator.logger.error(
            'Got error from API status request',
            error
          )
          if (error.message === 'connect ECONNREFUSED') {
            serviceLocator.logger.error(
              'Connection refused. Sending 500',
              error.message
            )
            return res.send(500, 'API Not available')
          }
          return next(error)
        }
        serviceLocator.logger.info(
          'Response received from API status',
          response.body
        )

        if (!response.body.setup) {
          serviceLocator.logger.info('Admin not setup, rendering setup page.')
          return res.render('setup', {
            config: serviceLocator.config,
            environment: serviceLocator.env
          })
        }
        next()
      }
    )
  }

  function getMessage(reason) {
    switch (reason) {
      case 'timeout':
        return 'Your session timed out. Please log in again.'
      case 'auth':
        return 'There was a problem with your access. Please try and log in again.'
      case 'reset':
        return 'Your password has been reset. Please log in.'
      case 'setup':
        return 'Please log in with your new details.'
      default:
    }
  }

  serviceLocator.router.get('/login', ensureSetup, function (req, res) {
    res.render('login', {
      config: serviceLocator.config,
      message: getMessage(req.query.reason),
      environment: serviceLocator.env
    })
  })

  serviceLocator.router.get('/saml-session-validation', function (req, res) {
    // check session index / id is valid. If not, redirect to saml-error page
    res.render('saml-session-validation', {
      config: serviceLocator.config
    })
  })

  serviceLocator.router.get('/password-reset-request', ensureSetup, function (
    req,
    res
  ) {
    res.render('password-reset-request', {
      config: serviceLocator.config,
      emailAddress: req.query.emailAddress || '',
      environment: serviceLocator.env
    })
  })

  serviceLocator.router.get('/password-reset', ensureSetup, function (
    req,
    res
  ) {
    res.render('password-reset', {
      config: serviceLocator.config,
      emailAddress: req.query.emailAddress,
      token: req.query.token,
      expiry: req.query.expiry,
      environment: serviceLocator.env
    })
  })

  serviceLocator.router.get('/config.json', function (req, res) {
    const adminDetailsUrl =
      serviceLocator.config.apiUrlInternal + '/config.json'
    const options = {
      url: adminDetailsUrl,
      headers: {
        authorization: req.headers.authorization,
        accept: req.headers.accept,
        'x-cf-date': req.headers['x-cf-date'],
        'x-cf-ttl': req.headers['x-cf-ttl'],
        'content-type': req.headers['content-type']
      }
    }
    request(options, function (error, response) {
      if (error) return res.sendStatus(500).json(error)
      if (response.statusCode !== 200) return res.send(response.statusCode)

      const config = pick(serviceLocator.config, configWhiteList)
      res.status(200).json(config)
    })
  })

  serviceLocator.router.get('/debug-sentry', function mainHandler(req, res) {
    throw new Error('My first ADMIN Sentry error!')
  })

  serviceLocator.router.get('/_health', createHealthCheckRoute())

  serviceLocator.router.get('/', renderApp)
  serviceLocator.router.get('/*', renderApp)
}
