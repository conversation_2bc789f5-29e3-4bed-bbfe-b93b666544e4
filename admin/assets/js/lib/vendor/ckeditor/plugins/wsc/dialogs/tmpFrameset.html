<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Frameset//EN" "http://www.w3.org/TR/html4/frameset.dtd">
<!--
Copyright (c) 2003-2015, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
-->
<html>
<head>
	<title></title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<script type="text/javascript">

function doLoadScript( url )
{
	if ( !url || typeof url !== 'string' )
		return false ;

	var s = document.createElement( "script" ) ;
	s.type = "text/javascript" ;
	s.src = url ;
	document.getElementsByTagName( "head" )[ 0 ].appendChild( s ) ;

	return true ;
}

var opener;
function tryLoad()
{
	opener = window.parent;

	// get access to global parameters
	var oParams = window.opener.oldFramesetPageParams;

	// make frameset rows string prepare
	var sFramesetRows = ( parseInt( oParams.firstframeh, 10 ) || '30') + ",*," + ( parseInt( oParams.thirdframeh, 10 ) || '150' ) + ',0' ;
	document.getElementById( 'itFrameset' ).rows = sFramesetRows ;

	// dynamic including init frames and crossdomain transport code
	// from config sproxy_js_frameset url
	var addScriptUrl = oParams.sproxy_js_frameset ;
	doLoadScript( addScriptUrl ) ;
}

	</script>
</head>

<frameset id="itFrameset" onload="tryLoad();" border="0" rows="30,*,*,0">
    <frame scrolling="no" framespacing="0" frameborder="0" noresize="noresize" marginheight="0" marginwidth="2" src="" name="navbar"></frame>
    <frame scrolling="auto" framespacing="0" frameborder="0" noresize="noresize" marginheight="0" marginwidth="0" src="" name="mid"></frame>
    <frame scrolling="no" framespacing="0" frameborder="0" noresize="noresize" marginheight="1" marginwidth="1" src="" name="bot"></frame>
    <frame scrolling="no" framespacing="0" frameborder="0" noresize="noresize" marginheight="1" marginwidth="1" src="" name="spellsuggestall"></frame>
</frameset>
</html>
