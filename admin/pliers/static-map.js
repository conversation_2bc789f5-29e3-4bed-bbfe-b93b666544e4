module.exports = task

const join = require('path').join

const fs = require('fs')
const versionator = require('versionator')

function task(pliers) {
  pliers('createStaticMap', function (done) {
    versionator.createMapFromPath(join(__dirname, '/../assets'), function (
      error,
      staticFileMap
    ) {
      if (error) return done(error)
      const prefixMap = {}
      let key
      for (key in staticFileMap)
        prefixMap['/assets' + key] = '/assets' + staticFileMap[key]
      fs.writeFileSync(
        join(__dirname, '/../static-file-map.json'),
        JSON.stringify(prefixMap, null, true)
      )
      done()
    })
  })
}
