module.exports = createTask
const join = require('path').join
const anyNewerFiles = require('any-newer-files')
const pliersModernizr = require('pliers-modernizr')

function createTask(pliers) {
  const destDir = join(__dirname, '/../assets/js/build/')

  pliers('buildModernizr', function (done) {
    if (
      !anyNewerFiles(
        pliers.filesets.modernizrConfig,
        pliers.filesets.modernizrCompiled
      )
    ) {
      pliers.logger.warn('No Modernizr changes found. No recompile required.')
      return done()
    }

    pliersModernizr(
      pliers,
      destDir
    )(function (err) {
      if (err) return done(err)
      done()
    })
  })
}
