module.exports = task

const join = require('path').join
const glob = require('glob')
const anyNewerFiles = require('any-newer-files')
const fs = require('fs')

function task(pliers) {
  pliers('writeBundleManifest', function (done) {
    if (!hasChanged()) {
      pliers.logger.warn(
        'No Browser JS changes found. Not writing bundle manifest js.'
      )
      return done()
    }

    const template = fs.readFileSync(
      join(__dirname, '/lib/bundle-manifest-template.js'),
      'utf8'
    )
    const componentGlobs = [
      join(__dirname, '/../../components/admin/**/init.js')
    ]
    let componentPaths = [].concat.apply(
      [],
      componentGlobs.map(function (path) {
        return glob.sync(path)
      })
    )

    componentPaths = componentPaths.map(function (componentPath) {
      const pathParts = componentPath.split('/')
      const componentIndex = pathParts.indexOf('components')
      const numPartsToKeep = pathParts.length - componentIndex
      const finalPath = pathParts
        .splice(componentIndex, numPartsToKeep)
        .join('/')
      return "require('../../../" + finalPath + "')"
    })

    const output = template.replace(
      '/* {{COMPONENT_LIST}} */',
      componentPaths.join('\n, ')
    )
    fs.writeFile(
      join(__dirname, '/../source/js/components.js'),
      output,
      (f) => f
    )
    done()
  })

  function hasChanged() {
    return anyNewerFiles(
      pliers.filesets.browserJs.concat(pliers.filesets.browserTemplates),
      pliers.filesets.browserJsCompiled
    )
  }
}
