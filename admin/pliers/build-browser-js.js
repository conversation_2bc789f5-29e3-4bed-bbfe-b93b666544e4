module.exports = createTasks
const join = require('path').join
const bundle = require('../../pliers/lib/create-browserify-bundler')
const writeBrowserJs = require('../../pliers/lib/write-browser-js')
const notify = require('../../pliers/lib/notify')
const watchify = require('watchify')
const async = require('async')
const basename = require('path').basename
const relative = require('path').relative
const projectRoot = join(__dirname, '/../../')
const destDir = join(__dirname, '/../assets/js/build/')
const anyNewerFiles = require('any-newer-files')
const browserSync = require('browser-sync')
const devNull = require('dev-null')

function createTasks(pliers, config) {
  pliers('buildBrowserJs', 'writeBundleManifest', function (done) {
    if (
      !anyNewerFiles(
        pliers.filesets.browserJs.concat(pliers.filesets.browserTemplates),
        pliers.filesets.browserJsCompiled
      )
    ) {
      pliers.logger.warn(
        'No Browser JS changes found. No JS recompile required.'
      )
      return done()
    }

    pliers.mkdirp(destDir)

    async.each(
      pliers.filesets.browserBundles,
      function (entry, cb) {
        const b = bundle(entry, pliers.filesets.vendorJs)
        const stream = b.bundle()
        const filename = basename(entry)
        const sourceMapWebPath = '/assets/js/build/' + filename + '.map'

        pliers.logger.debug('Building', filename)
        b.pipeline.on('error', cb)
        writeBrowserJs(pliers, stream, destDir, filename, sourceMapWebPath, cb)
      },
      done
    )
  })

  pliers('watchBrowserJs', function () {
    pliers.filesets.browserBundles.forEach(function (entry) {
      const b = bundle(entry, pliers.filesets.vendorJs)
      const filename = basename(entry)
      const sourceMapWebPath = '/assets/js/build/' + filename + '.map'

      pliers.logger.debug('Watching', entry)

      b.plugin(watchify).on('update', function (ids) {
        pliers.logger.debug(
          'JS watcher saw a change',
          ids.map(function (path) {
            return relative(projectRoot, path)
          })
        )
        var stream = b.bundle().on('error', function (err) {
          pliers.logger.error(err.message)
        })
        writeBrowserJs(
          pliers,
          stream,
          destDir,
          filename,
          sourceMapWebPath,
          function (err) {
            if (err) pliers.logger.error(err)
            notify('JS built', config.title + ' [admin] ' + filename)
            try {
              browserSync.get('admin').reload()
            } catch (e) {
              console.error(e)
            }
          }
        )
      })

      // Watchify won't start outputting 'update' events until it has done a full
      // AST walk. There's no way of doing this other than calling bundle(), which
      // obviously outputs the built JS. It won't start emitting events until this
      // the returned readable stream is drained, but since the JS build will be
      // unavailable while it's piping and it already exists, don't waste time
      // just pipe to /dev/null
      b.bundle()
        .pipe(devNull())
        .on('error', function (err) {
          pliers.logger.error(err.message)
        })
    })
  })
}
