module.exports = createFileset

const join = require('path').join

function createFileset(pliers) {
  const spriteImgDir = join(__dirname, '/../../assets/img/sprite')
  const spriteStylusDir = join(__dirname, '/../../source/stylus/sprite')

  pliers.filesets('spriteTemplate', spriteStylusDir + '/sprite.styl.tpl')

  pliers.filesets('spriteRaw', spriteImgDir + '/raw/**/*.svg')

  pliers.filesets('spriteCompiled', [
    spriteImgDir + '/generated/**/*.svg',
    spriteImgDir + '/generated/**/*.png',
    spriteStylusDir + '/sprite.styl'
  ])
}
