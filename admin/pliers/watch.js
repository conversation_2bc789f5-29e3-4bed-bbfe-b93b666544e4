module.exports = createTask

const notify = require('../../pliers/lib/notify')
const browserSync = require('browser-sync')

function createTask(pliers, config) {
  pliers('watch', function (done) {
    if (!browserSync.active) {
      browserSync
        .create('admin')
        .init({ logSnippet: false, port: config.adminBrowserSyncPort })
    }

    pliers.logger.info('Watching for stylus changes')
    pliers.watch(pliers.filesets.stylus, function () {
      pliers.run('buildCss', function () {
        notify('CSS built', config.title + ' Admin', config.url)
        browserSync.get('admin').reload(pliers.filesets.css)
      })
    })

    pliers.logger.info('Watching for Modernizr changes')
    pliers.watch(pliers.filesets.modernizrConfig, function () {
      pliers.run('buildModernizr', function () {
        notify('Modernizr built', config.title + ' Admin', config.url)
        browserSync.get('admin').reload()
      })
    })

    pliers.logger.info('Watching browser JS with watchify')
    pliers.run('watchBrowserJs')

    done()
  })
}
