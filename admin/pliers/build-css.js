module.exports = createTask

const join = require('path').join
const stylus = require('stylus')
const renderStylus = require('stylus-renderer')
const versionator = require('versionator')
const cleancss = require('./lib/clean-css')
const anyNewerFiles = require('any-newer-files')
const env = process.env.NODE_ENV || 'development'
const autoprefixer = require('autoprefixer-stylus')
const stylusMixins = require('stylus-mixins')
const responsiveGrid = require('responsive-grid')
const middleware = [
  autoprefixer(),
  stylusMixins(),
  responsiveGrid(),
  cleancss()
]

function createTask(pliers) {
  pliers('buildCss', function (done) {
    if (!anyNewerFiles(pliers.filesets.stylus, pliers.filesets.css)) {
      pliers.logger.warn('No Stylus changes found. No CSS recompile required.')
      return done()
    }

    const mappedVersion = versionator.createMapped(
      require('../static-file-map.json')
    )
    const stylesheets = pliers.filesets.stylesheets
    const srcDir = join(__dirname, '/../source/stylus')
    const destDir = join(__dirname, '/../assets/css')

    pliers.mkdirp(destDir)

    renderStylus(
      stylesheets,
      {
        src: srcDir,
        dest: destDir,
        use: middleware,
        stylusOptions: { compress: false },
        define: {
          $ENV: env,
          versionPath: function (urlPath) {
            return new stylus.nodes.Literal(
              'url(' + mappedVersion.versionPath(urlPath.val) + ')'
            )
          }
        }
      },
      function (err) {
        if (err) {
          pliers.logger.error('Failed to build CSS')
          pliers.logger.error(err.message)
        }
        done()
      }
    ).on('log', function (msg, level) {
      pliers.logger[level](msg)
    })
  })
}
