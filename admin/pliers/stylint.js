module.exports = createTask

const join = require('path').join

function createTask(pliers) {
  pliers('stylint', function (done) {
    let files
    const config = '.stylintrc'

    if (typeof process.argv[3] !== 'undefined') {
      files = join(__dirname, '/../source/stylus/') + process.argv[3] + '.styl'
    } else {
      files = join(__dirname, '/../source/stylus/')
    }

    pliers.exec('stylint ' + files + ' -c ' + config, done)
  })
}
