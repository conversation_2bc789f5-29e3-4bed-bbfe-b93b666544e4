module.exports = writeBuiltJs

const join = require('path').join
const exorcist = require('exorcist')
const fs = require('fs')
const compressJs = require('../../../pliers/lib/compress-js')
const compress = process.env.NODE_ENV !== undefined
const sourceMapsEnabled = !compress || !!process.env.ENABLE_SOURCE_MAPS

/*
 * Take a readstream (from browserify bundle) and write it to a file.
 * Also extracts the sourcemap and compresses the JS depending on env.
 */
function writeBuiltJs(pliers, readStream, site, destDir, filename, cb) {
  // Ensure build dir exists
  pliers.mkdirp(join(__dirname, '/../../assets/js/build'))

  const sourceMapWebPath = join('/assets/js/build', site, filename + '.map')
  let pipeChain = readStream

  // Costly to generate so only included upon request
  if (sourceMapsEnabled) {
    pipeChain = readStream.pipe(
      exorcist(join(destDir, filename + '.map'), sourceMapWebPath)
    )
  }

  pipeChain
    .pipe(fs.createWriteStream(join(destDir, filename), 'utf8'))
    .on('finish', complete)
    .on('error', cb)

  function complete() {
    pliers.logger.debug('Browser JS built', site.red, filename.blue)
    if (!compress) return cb(null)
    try {
      pliers.logger.warn('Compressing JS', filename)
      compressJs(destDir, filename, sourceMapWebPath, sourceMapsEnabled)
      cb(null)
    } catch (e) {
      cb(e)
    }
  }
}
