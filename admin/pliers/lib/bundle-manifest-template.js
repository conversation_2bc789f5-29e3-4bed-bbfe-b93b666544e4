// WARNING!
// This file is generated by pliers. Do not manually
// edit its contents. Your changes WILL be overwritten!

module.exports = initComponents

const componentLoader = require('component-loader')
const components = [
  /* {{COMPONENT_LIST}} */
]

function initComponents(serviceLocator) {
  componentLoader(
    components,
    function (initFn) {
      return initFn.bind(null, serviceLocator)
    },
    function (error) {
      if (error) throw error
      serviceLocator.componentLoader.emit('complete')
    }
  )
}
