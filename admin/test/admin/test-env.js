const createConfigury = require('@clocklimited/configury')
const join = require('path').join
const config = createConfigury(join(__dirname, '/../../../config.json'))(
  process.env.NODE_ENV
)
const { JSDOM } = require('jsdom')
const jq = require('../../assets/js/lib/vendor/jquery-3.1.1.min')
const backbone = require('backbone')

module.exports = function (cb) {
  global.jade = require('jade/lib/runtime')
  const document = new JSDOM('', { url: 'http://localhost' })
  const window = document.window
  window.config = Object.assign({}, config, {
    darkroom: {
      url: 'https://dr.io',
      salt: 'salty salamanders',
      key: 'keyey keys'
    }
  })
  global.window = window
  global.document = window.document
  global.Backbone = window.Backbone = backbone
  window.Backbone.window = window
  global.$ = global.jQuery = window.Backbone.$ = window.$ = window.jQuery = jq(
    window
  )
  global.navigator = window.navigator = { userAgent: 'node.js' }
  window.$.widget = function () {}
  window.CKEDITOR = { instances: [] }

  cb(null, window)
}
