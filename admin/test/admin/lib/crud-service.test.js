const rewire = require('rewire')
const OfferService = rewire('../../../source/js/lib/crud-service')
const assert = require('assert')
const UberCache = require('uber-cache')

describe('Ajax Crud Service', function () {
  describe('create()', function () {
    it('should emit a `create` event with the saved object', function (done) {
      OfferService.__set__('createAuthedRequester', function () {
        return function (method, path, data, cb) {
          cb(null, { statusCode: 201 }, { headline: 'hi' })
        }
      })

      const s = new OfferService({ config: {}, cache: new UberCache() })
      s.urlRoot = '/tests'
      s.on('create', function (obj) {
        assert.deepStrictEqual(obj, { headline: 'hi' })
        done()
      })

      s.create({}, function () {})
    })

    it('should hang validation errors off of an error object', function (done) {
      OfferService.__set__('createAuthedRequester', function () {
        return function (method, path, data, cb) {
          cb(null, { statusCode: 400 }, { errors: { a: 'invalid' } })
        }
      })

      const s = new OfferService({ config: {} })
      s.urlRoot = '/tests'
      s.create({}, function (err) {
        assert.strictEqual(err.message, 'Validation Error')
        assert.deepStrictEqual(err.errors, { a: 'invalid' })
        done()
      })
    })

    it('should handle a single error in the response', function (done) {
      OfferService.__set__('createAuthedRequester', function () {
        return function (method, path, data, cb) {
          cb(null, { statusCode: 400 }, { error: 'invalid json' })
        }
      })

      const s = new OfferService({ config: {} })
      s.urlRoot = '/tests'
      s.create({}, function (err) {
        assert(err instanceof Error)
        assert.strictEqual(err.message, 'invalid json')
        done()
      })
    })

    it('should handle a non-json error respose', function (done) {
      OfferService.__set__('createAuthedRequester', function () {
        return function (method, path, data, cb) {
          cb(null, { statusCode: 500 }, 'internal server error')
        }
      })

      const s = new OfferService({ config: {} })
      s.urlRoot = '/tests'
      s.create({}, function (err) {
        assert(err instanceof Error)
        assert.strictEqual(err.message, 'internal server error')
        done()
      })
    })
  })

  describe('find()', function () {
    it('should JSONify filter, sort and pagination query params', function (done) {
      OfferService.__set__('createAuthedRequester', function () {
        return function (method, path, query) {
          assert.deepStrictEqual(query, {
            keywords: 'jim',
            filter: JSON.stringify({ a: 10, b: 20 }),
            sort: JSON.stringify([['a', 'asc']]),
            pagination: JSON.stringify({ page: 1, pageSize: 20 })
          })
          done()
        }
      })

      const s = new OfferService({ config: {} })
      s.urlRoot = '/tests'
      s.find(
        'jim',
        { a: 10, b: 20 },
        [['a', 'asc']],
        { page: 1, pageSize: 20 },
        function () {}
      )
    })
  })

  describe('cachedFind()', function () {
    it('should get cleared on create/update/partialUpdate/delete', function () {
      let i = 0
      function mockCacheClear() {
        i += 1
      }

      OfferService.__set__('uberMemoize', function () {
        return function () {
          return { clear: mockCacheClear }
        }
      })

      const s = new OfferService({ config: {}, cache: new UberCache() })
      const events = ['create', 'update', 'partialUpdate', 'delete']

      events.forEach(s.emit.bind(s))

      assert.strictEqual(i, events.length)
    })
  })
})
