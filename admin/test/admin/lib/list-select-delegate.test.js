const listSelectDelegate = require('../../../source/js/lib/list-select-delegate')
const assert = require('assert')
const Backbone = require('backbone')
const testEnv = require('../test-env')

describe('list select delegate', function () {
  before(testEnv)

  it('should add a model to the selection on a "select" event', function () {
    const collection = new Backbone.Collection([
      { id: 1, name: '<PERSON>' },
      { id: 2, name: '<PERSON>' }
    ])
    const model = collection.at(1)
    const view = new Backbone.View({ collection: collection }).trigger()
    const selectedCollection = listSelectDelegate.call(view)

    model.trigger('select', model)
    assert.strictEqual(selectedCollection.length, 1)
  })

  it('should remove a model from the selection on a "deSelect" event', function () {
    const collection = new Backbone.Collection([
      { id: 1, name: '<PERSON>' },
      { id: 2, name: '<PERSON>' }
    ])
    const model = collection.at(1)
    const view = new Backbone.View({ collection: collection }).trigger()
    const selectedCollection = listSelectDelegate.call(view)

    model.trigger('select', model)
    assert.strictEqual(selectedCollection.length, 1)
    model.trigger('deSelect', model)
    assert.strictEqual(selectedCollection.length, 0)
  })

  it('should add all models to the selection on the "addToSelection" event', function () {
    const collection = new Backbone.Collection([
      { id: 1, name: 'Ben' },
      { id: 2, name: 'Paul' }
    ])
    const view = new Backbone.View({ collection: collection }).trigger()
    const selectedCollection = listSelectDelegate.call(view)

    view.trigger('addToSelection')
    assert.strictEqual(selectedCollection.length, 2)
  })

  it('should clear the selection on a "clearSelection" event', function () {
    const collection = new Backbone.Collection([
      { id: 1, name: 'Ben' },
      { id: 2, name: 'Paul' }
    ])
    const view = new Backbone.View({ collection: collection }).trigger()
    const selectedCollection = listSelectDelegate.call(view)

    view.trigger('addToSelection')
    assert.strictEqual(selectedCollection.length, 2)
    view.trigger('clearSelection')
    assert.strictEqual(selectedCollection.length, 0)
  })

  it('should remove an item even if it no longer exists in original collection', function () {
    const collection = new Backbone.Collection([
      { id: 1, name: 'Ben' },
      { id: 2, name: 'Paul' }
    ])
    const model = collection.at(1)
    const view = new Backbone.View({ collection: collection }).trigger()
    const selectedCollection = listSelectDelegate.call(view)

    model.trigger('select', model)
    assert.strictEqual(selectedCollection.length, 1)

    collection.reset()

    model.trigger('deSelect', model)
    assert.strictEqual(selectedCollection.length, 0)
  })
})
