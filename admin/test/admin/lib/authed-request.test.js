const assert = require('assert')
const rewire = require('rewire')
const createAuthedRequest = rewire('../../../source/js/lib/authed-request')
const testEnv = require('../../../test/admin/test-env')
const mockLocalStorage = {
  apiKey: '123',
  apiId: '324',
  apiTimeout: null
}

describe('#authedRequest', function () {
  before(function (done) {
    testEnv(function (err, window) {
      if (err) return done(err)

      delete window.localStorage

      window.localStorage = {
        getItem: function (item) {
          return mockLocalStorage[item]
        },
        setItem: function (item) {
          mockLocalStorage[item] = item
        }
      }
      done()
    })
  })

  it('should add text/plain content-type to req headers on DELETE requests if not set in options', function (done) {
    var requestMade = false
    createAuthedRequest.__set__('request', function (req, cb) {
      assert.strictEqual(
        req.headers['Content-Type'],
        'text/plain',
        "Didn't set content-type to text/plain"
      )
      requestMade = true
      cb()
    })

    var authedRequest = createAuthedRequest()
    authedRequest('DELETE', '/articles/1', {}, function (err) {
      if (err) return done(err)
      assert.ok(requestMade, 'request was never called')
      done()
    })
  })
})
