const assert = require('assert')
const formTitleDelegate = require('../../../source/js/lib/form-title-delegate')

describe('form title delegate', function () {
  var formTitle = formTitleDelegate('Name')

  it('should return new title if id is falsy', function () {
    assert.strictEqual(formTitle({ _id: null }), 'New Name')
    assert.strictEqual(formTitle({}), 'New Name')
  })

  it('should return edit title if id is set', function () {
    assert.strictEqual(formTitle({ _id: 1 }), 'Edit Name')
  })
})
