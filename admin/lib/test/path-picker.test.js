const assert = require('assert')
const pick = require('../path-picker')

describe('path-pick', function () {
  it('should return null', function () {
    assert.strictEqual(pick(null, []), null)
  })

  it('should return empty object on no selectors', function () {
    assert.deepStrictEqual(pick({ a: 1 }, []), {})
  })

  it('should select keys', function () {
    assert.deepStrictEqual(pick({ a: 1, b: 2 }, ['a']), { a: 1 })
  })

  it('should multiple select keys', function () {
    assert.deepStrictEqual(pick({ a: 1, b: 2, c: 3 }, ['a', 'c']), {
      a: 1,
      c: 3
    })
  })

  it('should selected path key', function () {
    assert.deepStrictEqual(pick({ a: 1, b: { z: 99 }, c: 3 }, ['b.z']), {
      b: { z: 99 }
    })
  })

  it('should selected path key with root key', function () {
    assert.deepStrictEqual(pick({ a: 1, b: { z: 99 }, c: 3 }, ['b.z', 'c']), {
      b: { z: 99 },
      c: 3
    })
  })

  it('should selected path key with ', function () {
    assert.deepStrictEqual(
      pick({ a: 1, b: { z: { j: 5, k: 6 } }, c: 3 }, ['b.z', 'c']),
      { b: { z: { j: 5, k: 6 } }, c: 3 }
    )
  })
})
