const join = require('path').join
const express = require('express')
const versionator = require('versionator')
const xFrameOptions = require('x-frame-options')
const uaCompatibleMiddleware = require('ua-compatible')
const responseTime = require('response-time')
const logger = require('morgan')
const redirectTrailingSlashMiddleware = require('@clocklimited/redirect-trailing-slash')
const mappedVersion = versionator.createMapped(
  require('./static-file-map.json')
)
const createSamlProviders = require('../components/service/saml/providers')

module.exports = function (serviceLocator) {
  const app = express()
  // One month static file expire on non-development
  const inDevelopmentMode = serviceLocator.env === 'development'
  const staticContentExpiry = inDevelopmentMode ? 0 : **********
  const logOptions = {
    stream: {
      write: function (data) {
        serviceLocator.logger.info((data + '').trim())
      }
    }
  }

  function setCacheHeaders(req, res, next) {
    res.set({ 'Cache-Control': 'max-age=60, must-revalidate', Expires: 60 })
    next()
  }

  function versionatorLocals(req, res, next) {
    res.locals.versionPath = mappedVersion.versionPath
    next()
  }

  function browserSyncLocals(req, res, next) {
    res.locals.enableBrowserSync =
      (process.env.NODE_ENV || 'development') === 'development'
    next()
  }

  const { sp, idp } = createSamlProviders()

  var nameId, sessionIndex
  // safely handles circular references
  JSON.safeStringify = (obj, indent = 2) => {
    let cache = []
    const retVal = JSON.stringify(
      obj,
      (key, value) =>
        typeof value === 'object' && value !== null
          ? cache.includes(value)
            ? undefined // Duplicate reference found, discard key
            : cache.push(value) && value // Store value in our collection
          : value,
      indent
    )
    cache = null
    return retVal
  }

  // Don't tell people we are express
  app
    .disable('x-powered-by')

    .set('views', join(__dirname, '/views/templates'))
    .set('view engine', 'jade')

    // Add response time as X-Response-Time header
    .use(responseTime())
    //
    .use(mappedVersion.middleware)
    // Wire up the express logger to the app logger
    .use(logger(inDevelopmentMode ? 'dev' : 'default', logOptions))
    // Gzip Compression for static assets
    .use(
      '/assets',
      express.static(join(__dirname, '/assets'), {
        maxAge: staticContentExpiry
      })
    )
    // Very little cache on the served pages
    .use(setCacheHeaders)

    // Make versionPath available in the jade templates
    .use(versionatorLocals)

    // Make BrowserSync available in the jade templates
    .use(browserSyncLocals)

    // X-Frame-Options response header to disallow site being iframed
    // See more here: https://developer.mozilla.org/en-US/docs/HTTP/X-Frame-Options.
    .use(xFrameOptions('Sameorigin'))

    // Redirect urls with a trailing slash
    .use(redirectTrailingSlashMiddleware)

    .use(uaCompatibleMiddleware)

    .use('/robots.txt', (req, res) => {
      res.sendFile(join(__dirname, '/robots.txt'))
    })

    // SAML authentication
    .use('/saml/metadata', (req, res) => {
      res.type('application/xml')
      const metaData = sp.create_metadata()
      serviceLocator.logger.info('metadata', metaData)
      res.send(metaData)
    })

    .use(express.urlencoded())

    .use('/saml/mock-acs', async (req, res) => {
      const sessionResponse = await fetch(
        'http://localhost.clockhosting.com:8112/create-saml-session',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            sessionIndex: '1234',
            email: '<EMAIL>'
          })
        }
      )
      const data = await sessionResponse.json()

      if (data.error) {
        serviceLocator.logger.error(data.error)
        return res.status(500).json(data)
      }

      if (!data.sessionIndex) {
        serviceLocator.logger.error('No session index found')
        return res.status(500).json({ error: 'No session index found' })
      }

      if (!data.email) {
        serviceLocator.logger.error('No email found')
        return res.status(500).json({ error: 'No email found' })
      }

      res.redirect(
        `http://localhost.clockhosting.com:8111/saml-session-validation?sessionIndex=${data.sessionIndex}&email=${data.email}`
      )
    })

    .use('/saml/login', (req, res) => {
      serviceLocator.logger.info('beginning login')
      sp.create_login_request_url(idp, {}, function (err, loginUrl, requestId) {
        serviceLocator.logger.info(
          'create_login_request_url',
          JSON.safeStringify(err),
          loginUrl,
          requestId
        )
        if (err != null) {
          serviceLocator.logger.error(err)
          return res.status(500).json(err)
        }
        res.redirect(loginUrl)
      })
    })

    .use('/saml/logout', (req, res) => {
      var options = {
        name_id: nameId,
        session_index: sessionIndex
      }

      sp.create_logout_request_url(idp, options, function (err, logoutUrl) {
        if (err != null) {
          serviceLocator.logger.error(err)
          return res.status(500).json(err)
        }
        res.redirect(logoutUrl)
      })
    })

  // App and router might not always be the same thing as app
  serviceLocator.register('server', app).register('router', app)

  return app
}
