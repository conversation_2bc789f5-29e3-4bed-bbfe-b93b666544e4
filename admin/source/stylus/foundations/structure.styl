//
// STRUCTURAL STYLES
// =================
//
// 1. Page Split
// 2. Page Content
// 3. Centering
//


//
// 1. PAGE SPLIT
// Splits the page into a fixed-width left sidebar, and flexi-width remainder.
//

.page-dark
  background-color #0f0f0f //@stylint ignore
  color white //@stylint ignore

  & *
    color white //@stylint ignore

.page-split
  display block

.page-split__left
  position fixed
  top 0
  bottom 0
  background-color $color--white
  box-shadow 0 0 6px rgba($color--black, 0.3)
  overflow auto
  width $sidebar-width
  +width-min($mq--desktop)
    width $sidebar-width--desktop
  .touchevents &
    width $sidebar-width--desktop

.page-split__flexi
  position relative
  margin-left $sidebar-width
  +width-min($mq--desktop)
    margin-left $sidebar-width--desktop
  .touchevents &
    margin-left $sidebar-width--desktop

//
// 2. PAGE CONTENT
// Wraps all content, other than site-header and navigation
//

.page-content
  padding-top rem(80)
  margin-bottom $base--spacing

.page-header
  display block


//
// 3. CENTERING
// Can be used to center non-floated content. Currently used within the
// toolbar and to center content within the `page-content` wrapper.
//

.centering
  clearfix()
  margin 0 auto
  max-width $base--max-width
  padding 0 $base--spacing * 0.5

  +width-min($mq--desktop)
    padding 0 $base--spacing

.centering-thin
  max-width $base--max-width--thin

//@stylint off

.centering-xy
  display flex
  height 100vh
  width 100vw
  justify-content center
  align-items center
  flex-direction column
  gap $base--spacing //@stylint ignore
  text-align center
  
.grid-n
  grid-template-columns 1fr
  +width-min($mq--desktop)
    gap 1rem
    display grid
    grid-template-columns 1fr 1fr
  +width-min($mq--desktop--large)
    grid-template-columns repeat(auto-fit, minmax(200px, 1fr))

.grid-3
  grid-template-columns 1fr
  +width-min($mq--desktop)
    display grid
    grid-template-columns 1fr 1fr 1fr
    & > *:nth-child(n + 1)
      margin-left 1rem
//@stylint on
