//
// ICONS
// =====
//


//
// BASE STYLES
// Some simple styles for other icons to extend and customise
//

$icon--base =
  display inline-block
  background-repeat no-repeat
  background-position 100% 100%
  line-height 1
  font-size 14px
  vertical-align top
  font-style normal

.icon
  hide-text()
  {$icon--base}
  background-image versionPath('/assets/img/sprite/generated/icon-sprite.png')
  .svg &
    background-image versionPath('/assets/img/sprite/generated/icon-sprite.svg')


//
// TAG ICONS
//

// Any tag types without an assigned icon should fall back to `general`
// Added before sprite include for specificity reasons
[class*='icon--tag-']
  {$icon--tag-general}


//
// ICONS SPRITE
// Generated automatically
//

@require '../sprite/sprite'


//
// MODIFIED ICONS
// Icons which need extra customisation
//

.icon--clock-important
  {$icon--clock}
  animation-name clock-important-fade, clock-important-move
  animation-duration 0.75s, 1.5s
  animation-iteration-count infinite
  animation-timing-function ease-in-out

@keyframes clock-important-fade
  0%
    opacity 0
  20%, 80%
    opacity 1
  100%
    opacity 0

@keyframes clock-important-move
  0%, 49.9%
    {$icon--clock}
  50%, 99.9%
    {$icon--important}
  100%
    {$icon--clock}


//
// CUSTOM ICONS
// Icons which don’t appear in the sprite
//

.icon-custom
  hide-text()
  {$icon--base}

.icon-custom--spinner
  box-shadow 0 1px 2px rgba($color--pure-black, 0.5)
  padding 12px
  width 16px
  height 16px
  background-color $color--white
  background-image versionPath('/assets/img/content/spinner.gif')
  background-position 50% 50%
  background-repeat no-repeat
  border-radius $base--border-radius

.icon--padded
  padding 0 4px

// New Icons
.icon-upcoming
  display flex
  width 8px
  height 8px
  background-color blue
  border-radius 100%
  box-shadow 0 0 2px blue

.icon-live
  display flex
  width 8px
  height 8px
  background-color red
  border-radius 100%
  box-shadow 0 0 2px red

.icon-past
  display flex
  width 8px
  height 8px
  background-color black
  border-radius 100%
  box-shadow 0 0 2px black
.loading-spinner
  position relative
  display flex
  width 50px
  height 50px

  &.center
    margin 0 auto

  &::after
    content ''
    position absolute
    width 100%
    height 100%
    border 4px solid #f3f3f3 //@stylint ignore
    border-top 4px solid $color--success
    border-radius 50%
    animation spin 1s linear infinite

@keyframes spin
  0%
    transform rotate(0deg)
  100%
    transform rotate(360deg)

.red-cross
  position relative
  display inline-block
  width 25px
  height 25px

  &::before,
  &::after
    content ''
    position absolute
    top 50%
    left 50%
    width 4px
    height 100%
    background-color red //@stylint ignore
    transform translate(-50%, -50%) rotate(45deg)

  &::after
    transform translate(-50%, -50%) rotate(-45deg)
