//
// CONTROLS
// ========
// Used to markup and style all form controls inputs, textareas, selects etc.
// For contol based components such as addons / timepickers, see `control-*.styl` components
//


$control--text
  display block
  width 100%
  padding $form--input-padding

  border 1px solid $color--border
  background-color $color--white
  border-radius 0
  box-shadow 0 0 0 1px $color--white inset

  font-family inherit
  font-size inherit
  line-height $base--line-height

  transition border-color 0.1s ease, box-shadow 0.2s ease

  // TODO Remove after refactor
  &.form-field
    width 100%
    +width-min($mq--desktop)
      width 70%

  &:focus
    border-color $color--border--focus
    box-shadow 0 0 5px rgba($color--border--focus, 0.5)
    outline 0

  .field--error &
    border-color tint($color--error, 65%)
    background-color tint($color--error, 95%)
    box-shadow 0 0 0 1px tint($color--error, 95%) inset
    &:focus
      box-shadow 0 0 0 1px tint($color--error, 65%) inset

.control--text
  @extend $control--text
  -webkit-appearance none

.control-text--60
  &.form-field
    +width-min($mq--desktop)
      width 60%

.control--choice
  @extend $control--text
  height 34px

.control--auto-height
  height auto

.control--multiline
  resize vertical
  height auto

.control--boolean
  margin-right 10px
  line-height inherit
  vertical-align middle

  & + span
    vertical-align middle

  span + &
    margin-right 0
    margin-left 10px

.control--thumbnail
  position relative
  margin-right 10px
  margin-top 10px !important
  width 300px
  height 166px
  appearance none
  overflow hidden
  &:before
    content ''
    position absolute
    top 20px
    right 10px
    z-index 1
  &:after
    content ''
    position absolute
    top 0
    left 0
    width 300px
    height 166px
    opacity 0.3
    {$icon--base}
    background-size cover
    background-image versionPath('/assets/img/content/grid-layouts.png')
    .svg &
      background-image versionPath('/assets/img/content/grid-layouts.svg')
  
  &:nth-child(2):after
    background-position 0 -166px
  &:nth-child(3):after
    background-position 0 -331.5px
  &:nth-child(4):after
    background-position 0 -496.5px
  &:nth-child(5):after
    background-position 0 -662px
  &:nth-child(6):after
    background-position 0 -827.5px
  &:nth-child(7):after
    background-position 0 -993px
  &:nth-child(8):after
    background-position 0 -1158px
  &:nth-child(9):after
    background-position 0 -1323.5px
  &:nth-child(10):after
    background-position 0 -1489px
  &:nth-child(11):after
    background-position 0 -1654.25px
  &:nth-child(12):after
    background-position 0 -1820px
  
.control--thumbnail:checked
  &:after
    opacity 1

.control--file
  line-height 1
  font-size inherit
  font-family inherit
