//
// VISIBILITY
// ==========
//


// REMOVE FROM DOCUMENT
// Only use if content has no SEO / Accessibility value

//@stylint off
.hidden
  display none !important
//@stylint on


// VISUALLY HIDE
// Still accessible to screen-readers/bots

//@stylint off
.vhidden
  position absolute !important
  clip rect(1px, 1px, 1px, 1px) !important
  overflow hidden !important
  height 1px !important
  width 1px !important
  padding 0 !important
  margin 0 !important
  border 0 !important

.disabled-div
  pointer-events none;
  opacity 0.25;

.disabled-div.loading
  position relative
  &::after
    content ''
    position absolute
    top 0%
    right 0%
    width 100px
    height 100px
    margin-right 10px
    margin-top 10px
    border 4px solid #f3f3f3
    border-top 4px solid $color--success
    border-radius 50%
    animation spin 1s linear infinite
    transform translate(-50%, -50%)

@keyframes spin
  0%
    transform rotate(0deg)
  100%
    transform rotate(360deg)
//@stylint on
