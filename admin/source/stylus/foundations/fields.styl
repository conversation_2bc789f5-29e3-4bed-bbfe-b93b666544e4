//
// FIELDS
// ======
// These lay out controls, labels and additional elements within the context
// of a form
//

.fieldset,
.field
  margin-bottom $base--spacing

.fieldset
  .field
    margin-bottom 0


//
// QUESTION
//

.field__question
  display block
  padding-bottom 4px
  +width-min($mq--desktop)
    float left
    width 30%
    padding-top 6px // Vertically align text with the input
    padding-right 20px

  // TODO - should this have it's own class
  // Required Field indicator
  abbr
    color $color--error
    margin-left 4px
    border-bottom none


//
// ANSWER
//

.field__answer
  display inline-block
  width 100%
  margin-bottom 0 // Useful when form-field is a `ul` etc.
  vertical-align top
  +width-min($mq--desktop)
    margin-left 30%
    width 70%

    .field__question + &
      margin-left 0

//
// FEEDBACK / ASSISTANCE
//

.field__assistance
.field__feedback
  display block
  margin-top 3px
  color $color--grey--mid
  font-size 0.8em

  +width-min($mq--desktop)
    margin-left 30%
    width 70%

  p, ul, ol
    margin-top 3px
    margin-bottom 0
