//
// RESET
//

// legend
//   padding-bottom .5em

// input, select, textarea
//   font-size 1em


//
// CONTROL STYLES
// Set some basic styles for inputs, selects and textareas
//

// .text-input
//   padding $form--input-padding
//   background-color $color--white
//   border 1px solid $color--border
//   line-height $base--line-height
//   transition border-color .1s ease, box-shadow .2s ease
//   font-family inherit
//   font-size inherit
//   &:focus
//     border-color $color--grey--mid
//     box-shadow 0 0 5px rgba($color--grey--mid, .75)
//     outline 0

// input[type='text']
// input[type='email']
// input[type='tel']
// input[type='url']
// input[type='password']
// input[type='search']
// input[type=number]
//   @extend .text-input

// textarea
//   @extend .text-input
//   resize vertical
//   vertical-align bottom

// select
//   @extend .text-input
//   height 34px

// select[multiple]
//   display inline-block
//   height auto
//   padding 5px 10px

// input[type='file']
//   line-height 1
//   font-family inherit
//   font-size inherit

//
// CKEDITOR INPUT
//

.form-row--ckeditor
  // TODO Temp styles during form refactor
  .cke
    width 70%
    margin-left 30%


//
// FORM - Horizontal
//
.form-horizontal
  display grid
  grid-auto-flow column
  align-items flex-end
  padding-bottom $base--spacing
  gap ($base--spacing / 2) //@stylint ignore

  *
    margin-bottom 0 !important


//
// FORM LABEL TEXT
// The question part of a form row
//

.form-label-text
  display block
  padding-bottom 4px
  +width-min($mq--desktop)
    float left
    width 30%
    padding-top 6px // Vertically align text with the input
    padding-right 20px

  // Required Field indicator
  abbr
    color $color--error
    margin-left 4px
    border-bottom none

.form-label-text--40
  width 40%

.form-label-text__right
  float right

.form-label-text-full
  float none
  width 100%
  padding-right 0


//
// FORM FIELD
// This anwser part of a form row
//

.form-field
  display inline-block
  width 100%
  margin-bottom 0 // Useful when form-field is a `ul` etc.
  vertical-align top
  +width-min($mq--desktop)
    width 70%

  ul, ol, dl
    margin-bottom 0

  // Reset default list styles when a list is used as the `.form-field`
  li
    list-style none
    margin-left 0

  // When inputs appear within a `.form-field`, give them these extra styles.
  input[type='text']
  input[type='email']
  input[type='tel']
  input[type='url']
  input[type='password']
  input[type='search']
  input[type=number]
  textarea,
  select
    display block
    width 100%

  span&.control
    padding-top rem(6)

.form-field--thumbnails
  +width-min($mq--desktop)
    margin-left 30%


//
// FORM COPY
// Provides basic styling which is used by input errors and descriptions.
//

.form-copy
  margin-top 3px
  p, ul, ol
    margin-top 3px
    margin-bottom 0
    color $color--grey--mid
    font-size 0.8em

.form-row-description
  display block
  +width-min($mq--desktop)
    margin-left 30%
    width 70%

.form-row-full-width
  .form-row-description
    margin-left 0
    width 100%

//
// FORM ROW
// This wrapping element forms the basis of any form
//

.form-row
  margin-bottom $base--spacing

  input[type='checkbox']
  input[type='radio']
    margin-right 10px
    vertical-align top
    margin-top 5px

  input[type='radio']
    margin-top 4px


//
// 5. FORM ACTIONS
// Normally contains Submit and Reset inputs.
//

.form-row-actions
  text-align right

  .btn + .btn
    margin-left 10px

  .form-row-actions-left
    float left

  .form-row-actions-center
    text-align center


//
// FORM ROW MODIFIERS
//

.form-row-boolean
.form-row-upload
.form-row-output
.form-row-label-top
  .form-label-text
    padding-top 0

.form-row-boolean
  .form-field
    label
      display inline-block

//
// SWITCHED POSITION CHECKBOXES
// Display the label text and field inline, when the field has to come first.
//

.form-row-boolean-reverse
  .form-field
    +width-min($mq--desktop)
      margin-left 30%


//
// FORM ROW MULTI SELECT
//

.form-field-multi-select

  li
    margin-bottom 6px

  label + label
    margin-top 6px
    margin-bottom $base--spacing

//
// FULL WIDTH INPUTS
// Inputs appear full width below labels.
//

.form-row-full-width
  // TODO Temp styles during form refactor
  .control.form-field
    width 100%
  .form-label-text
    display block
    float none
    width 100%
    padding-right 0
    padding-top 0
  .form-field
    width 100%
    margin-left 0
  .form-row-description
    width 100%
    margin-left 0


//
// FORM OVERVIEW ERROR
// Appears at the top of the form to highlight that validation errors occured.
// May also list the individual error messages if required.
//

.form-error
  margin-bottom $base--spacing
  background-color $color--error
  border-radius $base--border-radius
  padding rem(10) rem(15)
  color $color--white

  *
    color $color--white

  a
    text-decoration none
    color $color--white


//
// MESSAGE STYLING FOR INPUTS
// Highlight and display inline messages
//

.form-row-error-text
.form-row-success-text
.form-row-warning-text
.form-row-notice-text
  color $color--white
  padding $form--input-padding
  border-radius $base--border-radius
  margin-top rem(5)

.form-row-error-text
  background-color $color--error

.form-row-success-text
  background-color $color--success

.form-row-warning-text
  background-color $color--warning

.form-row-notice-text
  background-color $color--notice


//
// Form Control Additions
//





//
// CONTROL ADDONS
//



//
// CONTROL IMAGE PREVIEW
//

.control-image-preview
  margin-top 10px

.control-image-preview-inner
  display inline-block
  background-color $color--white
  border 1px solid $color--border
  box-shadow 0 1px 2px rgba($color--pure-black, 0.1)
  background-image versionPath('/assets/img/content/checkerboard-bg.gif')
  background-position 1px 1px
  background-repeat repeat


//
// Form error message highlighting
//

.form-row:target
  .form-row-error-text
    animation-name form-row-highlight
    animation-duration 0.5s
    animation-timing-function ease
    animation-iteration-count 2

@keyframes form-row-highlight
  0%, 100%
    opacity 1
  50%
    opacity 0.5

//
// Toggle
//
.toggle
  margin-bottom 12px

  input[type='checkbox']
    display none

  label
    color black //@stylint ignore
    position relative

  input[type='checkbox'] + label::before
    content ' '
    display block
    height 18px
    width 35px
    border 1px solid $color--border
    border-radius 9px
    position absolute
    top 0
    left -45px
    background $color--border

  input[type='checkbox'] + label::after
    content ' '
    display block
    height 14px
    width 14px
    border 1px solid grey //@stylint ignore
    border-radius 50%
    position absolute
    top 2px
    left -43px
    background grey //@stylint ignore
    transition all 0.3s ease-in

  input[type='checkbox']:checked + label::after
    left -27px
    border 1px solid $color--success
    background $color--success
    transition all 0.3s ease-in