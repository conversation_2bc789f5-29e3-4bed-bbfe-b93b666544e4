//
// MAIN HEADER
// ===========
//


.main-header
  margin-bottom ($base--spacing / 1.5)
  display table
  width 100%

.main-header__inner
  position relative
  display table-cell
  height 100px
  vertical-align middle
  background-color $color--black

.site-title
  text-align center
  height 100%
  overflow hidden
  a
    height 100%
    background-image versionPath('/assets/img/content/logo.svg')
    background-size auto 80%
    background-position 50% 50%
    background-repeat no-repeat
    display block
    color transparent !important
    transform scale(1)
    transition transform 0.3s ease-in-out

    &:hover
      transform scale(1.05)
      transition-duration 0.1s

.environment-flag
  position absolute
  top 10px
  right 0
  left 0
  display block
  text-align center
  text-transform uppercase
  line-height 1
  color $color--white
  font-weight bold
  letter-spacing 0.1em
  text-shadow -1px -1px 0 $color--black, 1px -1px 0 $color--black,
      -1px 1px 0 $color--black, 1px 1px 0 $color--black, 0 0 3px $color--black

  // Extra styles if used on a link
  a&
    text-decoration none
  a&:hover
    color $color--white
    text-decoration underline
