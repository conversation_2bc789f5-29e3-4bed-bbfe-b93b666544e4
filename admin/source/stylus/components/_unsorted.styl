//
// UNSORTED
// Remains of the old sandbox file, to be sorted to better places
//

// TODO - relocate this to live with other styles
// May be made obsolete with summaries refactor
.image-select-preview
  position relative
  // width 160px
  min-height 150px
  // height 160px

//
// 'wide' label on section layout manager
//
.layout-row-label
  margin-top -5px
  +width-min($mq--desktop--medium)
    position absolute
    top 0
    right 100%
    margin-top 0
    margin-right 10px

// Draggable hamburger icons
.sort-handle
  cursor move !important //@stylint ignore // May not need the important when this has a final place

// Icon within h2 as column titles on layout manager page.
// Refactor so icon doesn't like within h2
h2 .label
  vertical-align middle

//
// Voucher image asset preview
// (offer form -> voucher redemption type)
//
.asset-preview img
  margin-top 10px

//
// List Items on the Section Widget Organiser pages
//

.widget-row
.widget-drop-area

  .content-list-lists
    margin-bottom 1em
    &:empty
      display none
      & + hr
        display none
    .list-item
      margin-bottom 5px
    .list-item-header
      padding 5px
    .label--large
      margin-right 5px

  hr
    margin-bottom 10px

  dl
    clearfix()
    float left
    margin-right 0
  dt
    clear left
    float left
    margin-right 5px
  dd
    float left
    margin-bottom 0
    margin-right 0
    word-wrap break-word

  p
    margin-bottom 0

  // Fails linting, but will go in the list-item -> summary refactor
  p ~ *
    margin-top 5px

  iframe
    border 1px solid $color--border
    margin-left -1px
    margin-right -1px


//
// Selected section text list
// Appears on article form and auto-list form
//

.list-opening-time-exceptions
  .label
    margin-right 5px

// This makes the draggble article list items
// on the manual list form be the correct width,
// otherwise they go 100% of the window
.list-grid
  position relative

// Condensed filter that appears on the manual list form
// e.g. supplier picker widget overlay - manual form
.filter-condensed
  border-bottom 1px solid $color--border
  margin-bottom 1.5em
  .form-row
    margin-bottom ($base--spacing / 2)





//
// Label for section list items to display their instance
// (these element are also styled with style='..' to make
// use of the dynamic accent colours)
//
.instance-label
  padding 0 3px
  border-radius 2px
  float left
  font-size 11px
  margin 0 6px 0 0

  img
    height 23px
    border-radius 3px
    vertical-align middle
    transform translateY(-2px)

  // Instance label in a selectize input
  // (offer form -> Sections: Instance section(s))
  .selectize-control &
    font-size 10px
    margin 4px 5px 4px 0

    img
      height 12px
      transform none

//
// This ensure the textarea used to display
// the changelog on /changelog admin route
// behaves correctly
//
.changelog
  width 100%
  min-width 100%
  max-width 100%
  min-height 50vh
  max-height 100vh
  margin-bottom 15px
