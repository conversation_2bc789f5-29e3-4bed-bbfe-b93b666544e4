//
// PANELS
// ======
// Panels are used to visually contain elements. They have no structural styles
// other than some basic spacing. Often, these will appear directly within
// the page content, for full-width panels, or directly within grid items.
//


//
// BASIC PANEL
//

.panel
  margin-bottom 20px


//
// STYLED PANEL
// Can be made up of a nested panel-header and panel-content if required.
// Contains no direct padding - this can be achieved using padding-content.
//

// TODO: See if panel is ever used without panel-styled. Refactor if not
.panel-styled
  background-color $color--white
  border 1px solid $color--border
  box-shadow 0 1px 2px rgba($color--pure-black, 0.1)

  // &--dark
  //   background-color $color--pure-black
  //   border-color $color--pure-black
  //   color $color--white

  //   .panel-header--dark
  //     background-color $color--pure-black
  //     color $color--white

  //     h2
  //       color $color--white

.panel-green
  border 4px solid $color--green
  position relative
  padding 20px
  box-shadow 0 0 0px $color--green //@stylint ignore
  animation glowAnimation 2s infinite

@keyframes glowAnimation
  0%
    box-shadow 0 0 0px $color--green //@stylint ignore
  50%
    box-shadow 0 0 5px $color--green //@stylint ignore
  100%
    box-shadow 0 0 0px $color--green //@stylint ignore

.panel-header
  padding 20px
  background-color $color--white

  &.flex
    display flex

  h2
    display inline-block
    margin-bottom 0

  abbr
    border-bottom none

  p
    margin-bottom 0
    margin-top 0.5em

  .panel-leading-actions
    margin-right 10px
    float left

  .panel-actions
    float right
    .btn
    .js-tooltip-trigger
      margin-left 10px
    .btn-group
      .btn
        margin-left -1px
  
  .inline-action
    display flex
    justify-content space-between

    h3
      margin-bottom 0
    

.panel-header--justified
  display flex
  justify-content space-between
  flex-wrap wrap

.panel-content
  padding 20px 20px 0

.panel-content--padding-bottom
  padding-bottom 20px

// If panel-header is directly followed by panel-content, add a separating border.
.panel-header + .panel-content
  border-top 1px solid $color--border

.no-panel-styled .panel-styled
  all unset !important




//
// FIXED POSITION PANEL
//

.panel-fixed
  position fixed
  width 233px // TODO - only works for the one-quarter filter box at the minute - not responsive width
  z-index 1

.panel-unfixed
  position static


.panel:target
  animation-name section-highlight
  animation-duration 5s
  animation-timing-function ease
  animation-iteration-count 1

@keyframes section-highlight
  0%
    box-shadow 0 1px 2px rgba($color--pure-black, 0.1)
  10%
    box-shadow 0 0 20px rgba($color--primary, 1)
  50%
    box-shadow 0 0 20px rgba($color--primary, 1)
  100%
    box-shadow 0 1px 2px rgba($color--pure-black, 0.1)


//
// STICKY PANEL
//

.panel-sticky-container
  position relative

.panel-sticky
  position fixed
  width 233px // TODO - only works for the one-quarter filter box at the minute - not responsive width
  z-index 1
  top 50px + $grid--gutter // needs height of toolbar

.panel-sticky-end
  position absolute
  bottom 0

.js-toggle-content
  cursor pointer
  transition background-color 0.2s ease
  &:hover
    background-color shade(white, 5%)
