//
// TOOLTIPS
// ========
//

$tooltip--arrow-width = 5px
$tooltip--max-width = 200px
$tooltip--background-color = $color--black


.tooltip
  position absolute
  z-index zi('tooltip')
  display block
  font-family inherit
  font-size rem(12)
  font-weight normal
  line-height 1.4
  opacity 0

  &.in
    opacity 1
  &.top
    margin-top -3px
    padding $tooltip--arrow-width 0
  &.right
    margin-left 3px
    padding 0 $tooltip--arrow-width
  &.bottom
    margin-top 3px
    padding $tooltip--arrow-width 0
  &.left
    margin-left -3px
    padding 0 $tooltip--arrow-width

.tooltip-inner
  max-width $tooltip--max-width
  padding 3px 8px
  color $color--white
  text-align center
  text-decoration none
  background-color $tooltip--background-color
  border-radius $base--border-radius
  white-space normal

.tooltip-arrow
  position absolute
  width 0
  height 0
  border-color transparent
  border-style solid

  .tooltip.top &
    bottom 0
    left 50%
    margin-left -($tooltip--arrow-width)
    border-width $tooltip--arrow-width $tooltip--arrow-width 0
    border-top-color $tooltip--background-color

  .tooltip.right &
    top 50%
    left 0
    margin-top -($tooltip--arrow-width)
    border-width $tooltip--arrow-width $tooltip--arrow-width $tooltip--arrow-width 0
    border-right-color $tooltip--background-color

  .tooltip.left &
    top 50%
    right 0
    margin-top -($tooltip--arrow-width)
    border-width $tooltip--arrow-width 0 $tooltip--arrow-width $tooltip--arrow-width
    border-left-color $tooltip--background-color

  .tooltip.bottom &
    top 0
    left 50%
    margin-left -($tooltip--arrow-width)
    border-width 0 $tooltip--arrow-width $tooltip--arrow-width
    border-bottom-color $tooltip--background-color


.tooltip__disabled-wrapper
  display inline-block
