//
// PROGRESS BARS
// =============
//

$progress--height = 8px
$progress--bg-size = $progress--height * 2


//
// BASE STYLES
//

.progress
  overflow hidden
  margin-bottom $base--spacing
  height $progress--height
  background-color $color--grey--light
  box-shadow inset 0 1px 2px rgba($color--black, 0.1)
  border-radius ($progress--height / 2)

.progress__bar
  float left
  width 0
  height 100%
  background-color $color--default
  box-shadow inset 0 -1px 0 rgba($color--black, 0.15)

  &.is-active
    transition width 0.6s ease


//
// STATE MODIFIERS
//

.progress__bar--success
  background-color $color--success

.progress__bar--notice
  background-color $color--notice

.progress__bar--warning
  background-color $color--warning

.progress__bar--error
  background-color $color--error


//
// STYLE MODIFIERS
//

.progress__bar--striped
  background-image linear-gradient(-45deg, rgba($color--white, 0.15) 25%, transparent 25%, transparent 50%, rgba($color--white, 0.15) 50%, rgba($color--white, 0.15) 75%, transparent 75%, transparent)
  background-size $progress--bg-size $progress--bg-size

  &.is-active
    animation progress-bar-stripes 1s linear infinite

@keyframes progress-bar-stripes
  0%
    background-position $progress--bg-size 0
  100%
    background-position 0 0
