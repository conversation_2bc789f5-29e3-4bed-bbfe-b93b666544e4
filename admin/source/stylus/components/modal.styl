//
// MODALS
// ======
//


//
// OVERLAY
//

.modal-overlay
  position fixed
  top 0
  left 0
  right 0
  bottom 0
  background-color rgba($color--black, 0.45)
  overflow-y auto
  z-index zi('modal-overlay')


//
// CONTENT WRAPPER
//

.modal-content
  width 400px
  position relative
  top 30% // Default - Overridden with calculated JS value
  left 0
  right 0
  margin auto
  padding $base--spacing--small
  border-radius $base--border-radius
  background-color $color--white
  box-shadow 0 2px 5px rgba($color--black, 0.3)

  &.medium
    width 620px

  &.wide
    width 1000px


//
// CONTENT ELEMENTS
//

.modal-content

  h1
    font-size 25px
    padding-bottom ($base--spacing / 2)
    margin-bottom $base--spacing
    border-bottom 1px solid $color--border


.modal-controls
  text-align right

  // TODO: see if this can be made more global
  .btn + .btn
    margin-left 10px

.modal--center
  top 50% !important
  transform translateY(-50%) !important

.modal--large
  width 550px

.modal--wide
  width 700px
