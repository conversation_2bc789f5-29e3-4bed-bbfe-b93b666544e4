//
// ROW LAYOUTS
// ===========
// Appear in the layout manager, displaying icons representing possible row layouts
//


.row-layouts

  p
    margin-bottom 10px

.row-layouts__button
  width 100px
  text-align center
  text-decoration none
  line-height 1.2
  margin-right 20px
  margin-bottom 10px
  &:last-child
    margin-left 0

  .row-icon
    opacity 0.8
    transition opacity 0.2s ease

  &:hover
    text-decoration none

    .row-icon
      box-shadow 0 0 0 1px $color--primary inset
      border-radius 3px 3px 0 0
      opacity 1

  &:focus
    outline 0


//
// ROW ICONS
//

.row-icon
  {$icon--base}
  width 100px
  height 85px
  margin-bottom 6px
  background-image versionPath('/assets/img/content/row-layouts.png')
  .svg &
    background-image versionPath('/assets/img/content/row-layouts.svg')

.row-icon--4
  background-position 0 0

.row-icon--4-wide
  background-position 0 -85px

.row-icon--3-1
  background-position 0 -170px

.row-icon--1-3
  background-position 0 -255px

.row-icon--2-2
  background-position 0 -340px

.row-icon--1-2-1
  background-position 0 -425px

.row-icon--2-1-1
  background-position 0 -510px
