//
// FILE TYPE ICONS
// ===============
//


.button-file-type
  display inline-block
  text-align center
  padding 8px 4px 0
  background-color $color--white
  text-decoration none

  span
    display block
    word-break break-all
    overflow hidden
    text-overflow ellipsis
    line-height 14px
    height 14px * 5

  &:hover
    text-decoration none
    span
      text-decoration underline


// Main body of the icon
.file-icon
  position relative
  margin-top 20px
  margin-left auto
  margin-right auto
  margin-bottom 5px
  display block
  background-color $color--base
  width 50px
  height 45px
  text-align center
  line-height 36px
  text-transform uppercase
  font-style normal
  color $color--white
  font-size 12px
  letter-spacing 1px

// Top area of icon, missing top-right corner
.file-icon:before
  content ''
  width 50px
  height 0
  position absolute
  bottom 100%
  top -20px
  left 0
  border-style solid
  border-width 0 20px 20px 0
  border-color transparent
  border-bottom-color $color--base
  background-color transparent !important //@stylint ignore - override specificity of custom file-type colour

// Folder corner overlay
.file-icon:after
  content ''
  width 0
  height 0
  position absolute
  bottom 100%
  right 0
  border-style solid
  border-width 10px
  border-color transparent
  border-left-color $color--pure-black
  border-bottom-color $color--pure-black
  opacity 0.3


//
// CUSTOM ICON COLOURS
// ===================
//

// TODO: set up hash/object of colours and file types to avoid duplication
//@stylint off
.file-icon--psd
.file-icon--psd:before
  $file-icon-color = #2e6ae4
  background-color #2e6ae4
  border-bottom-color #2e6ae4

.file-icon--pdf
.file-icon--pdf:before
  background-color #c51e09
  border-bottom-color #c51e09

.file-icon--rtf
.file-icon--rtf:before
.file-icon--txt
.file-icon--txt:before
.file-icon--csv
.file-icon--csv:before
  background-color #777
  border-bottom-color #777

.file-icon--html
.file-icon--html:before
  background-color #2c9864
  border-bottom-color #2c9864

.file-icon--css
.file-icon--css:before
  background-color #c78d4a
  border-bottom-color #c78d4a

.file-icon--doc
.file-icon--doc:before
.file-icon--docx
.file-icon--docx:before
  background-color #1b0091
  border-bottom-color #1b0091

.file-icon--xlr
.file-icon--xlr:before
.file-icon--xls
.file-icon--xls:before
.file-icon--xlsx
.file-icon--xlsx:before
  background-color #007c33
  border-bottom-color #007c33

.file-icon--pps
.file-icon--pps:before
.file-icon--ppt
.file-icon--ppt:before
.file-icon--pptx
.file-icon--pptx:before
  background-color #ea4d00
  border-bottom-color #ea4d00
//@stylint on
