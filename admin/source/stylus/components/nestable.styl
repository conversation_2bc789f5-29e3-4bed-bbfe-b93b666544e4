//
// NESTABLE
// ========
// Drag and drop plugin
// https://github.com/dbushell/Nestable
//

//
// LISTS
//

.dd
.dd-list
  position relative
  display block
  margin 0
  padding 0
  list-style none

// Nested Lists
.dd-list .dd-list
  padding-left 30px

.dd-collapsed .dd-list
  display none


//
// DRAGGABLE ITEMS
//

.dd-item,
.dd-empty,
.dd-placeholder
  display block
  position relative
  margin 0
  padding 0
  min-height 50px


// Individual draggable elements
.dd-handle
  background-color $color--white
  border 1px solid $color--border
  box-shadow 0 1px 2px rgba($color--pure-black, 0.1)
  position relative
  margin-bottom 20px
  padding 20px
  min-height 30px

  h2
  p
    margin-bottom 0
    line-height 30px
    ellipsis()

  .dd-actions
    float right
    margin-top 2px

  // Visible drag handle
  .dd-handle-icon
    position absolute
    top 0
    left 0
    bottom 0
    width 40px
    background-color $color--base
    &:before
      content '≡'
      display block
      position absolute
      left 0
      top 50%
      margin-top -17px
      width 100%
      text-align center
      text-indent 0
      color $color--white
      font-size 20px
      font-weight normal

.dd-handle--thin
  padding 0 20px

.dd-handle--inline
  display inline-block
  vertical-align top

.dd-item-content
  display inline-block
  width calc(100% - 42px)

.dd-handle
  cursor move
  &:hover
    .dd-handle-icon
      background-color $color--base

.dd-handle-icon
  display block

  + .dd-item-content
    margin-left 40px


// Target area when dragging
.dd-placeholder
  border 1px dashed $color--base
  background-color $color--grey
  margin 10px 0
  min-height 70px


// The item being dragged
.dd-dragel
  position absolute
  pointer-events none
  z-index 9999

  .dd-handle
    box-shadow 0 2px 7px -3px rgba($color--pure-black, 0.5)
    transform scale(1.02)
    border-color $color--base
