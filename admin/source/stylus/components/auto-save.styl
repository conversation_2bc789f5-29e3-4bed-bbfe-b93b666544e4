//
// AUTO-SAVE INDICATOR
// ===================
// Styles for the auto-save indicator that appears during auto-save operations
//

.auto-save-indicator
  position fixed
  top 60px
  right 20px
  background-color rgba($color--success, 0.9)
  color $color--white
  padding 8px 12px
  border-radius $base--border-radius
  font-size rem(12)
  z-index zi('foreground-notification')
  box-shadow 0 2px 4px rgba($color--pure-black, 0.2)
  transition opacity 0.3s ease

  &.auto-save-indicator--error
    background-color rgba($color--error, 0.9)

  &.auto-save-indicator--saving
    background-color rgba($color--notice, 0.9)

    &:after
      content '...'
      animation auto-save-dots 1.5s infinite

//
// AUTO-SAVE STATUS
// ================
// Persistent status indicator in the toolbar area
//

.auto-save-status
  position fixed
  top 10px
  left 50%
  transform translateX(-50%)
  background-color rgba($color--white, 0.95)
  border 1px solid $color--border
  border-radius $base--border-radius
  padding 8px 12px
  font-size rem(11)
  color $color--black
  box-shadow 0 1px 3px rgba($color--pure-black, 0.1)
  z-index zi('toolbar') - 1
  display flex
  align-items center
  gap 8px //@stylint ignore

  &__label
    font-weight bold
    color $color--base

  &__text
    color $color--success

    &.saving
      color $color--notice

    &.error
      color $color--error

  &__time
    color $color--base
    font-size rem(10)

//
// LAST MODIFIED INDICATOR
// =======================
// Shows when the article was last modified under the page title
//

.last-modified
  margin-top 5px
  margin-bottom 15px

  &__text
    color $color--base
    font-size rem(12)
    font-style italic

    .js-last-modified-time
      color $color--black
      font-weight normal

@keyframes auto-save-dots
  0%, 20%
    opacity 0
  50%
    opacity 1
  100%
    opacity 0
