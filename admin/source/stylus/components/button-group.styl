//
// BUTTON GROUP
// ============
//


.btn-group
  display inline-block
  position relative
  font-size 0
  vertical-align middle
  white-space nowrap

  // TODO: Remove after spaced-group refactor
  & + &
    margin-left rem(10)
  .spaced-group--condensed
    margin-left rem(5)

  > .btn
    position relative
    border-radius 0

  > .btn + .btn
    margin-left -1px

  > .btn
  > .dropdown-menu
  > .popover
  > .text-btn
    font-size rem(14)

  > .btn--small
    font-size rem(12)

  > .btn:first-child
    margin-left 0
    border-top-left-radius $base--border-radius
    border-bottom-left-radius $base--border-radius

  > .btn:last-child
  > .dropdown-toggle
    border-top-right-radius $base--border-radius
    border-bottom-right-radius $base--border-radius

  > .btn:hover
  > .btn:focus
  > .btn:active
    z-index 2

  > .btn + .dropdown-toggle
    padding-left rem(8)
    padding-right rem(8)

  &.open .btn.dropdown-toggle
    border-color shade($color--grey--light, 25%)
    background-color shade($color--grey--light, 15%)

  &.open .btn--action.dropdown-toggle
    border-color shade($color--action, 25%)
    background-color shade($color--action, 15%)

  &.open .btn--success.dropdown-toggle
    border-color shade($color--success, 25%)
    background-color shade($color--success, 15%)

  &.open .btn--notice.dropdown-toggle
    border-color shade($color--notice, 25%)
    background-color shade($color--notice, 15%)

  &.open .btn--warning.dropdown-toggle
    border-color shade($color--warning, 25%)
    background-color shade($color--warning, 15%)

  &.open .btn--error.dropdown-toggle
    border-color shade($color--error, 25%)
    background-color shade($color--error, 15%)


//
// CARET
//

.btn .caret
  margin-top 8px
  margin-left 0

.text-btn .caret
  margin-top 8px
  margin-left 4px

.btn--action
.btn--success
.btn--notice
.btn--warning
.btn--error
  .caret
    border-top-color $color--white
    border-bottom-color $color--white
