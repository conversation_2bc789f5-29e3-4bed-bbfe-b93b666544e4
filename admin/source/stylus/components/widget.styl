//
// WIDGET MANAGER
// ==============
//
// 1. Widget List Inline
// 2. Widget Row
// 3. Widget
// 4. Widget Expander
//


// TODO: refactor to use base colours
$widget-row--background = #eaeaea
$ui-sortable-placeholder--background = #bbb
$widget-drop-area--border = #aaa
$widget-drop-area--background = #f8f8f8


//
// 1. WIDGET LIST INLINE
// Displayed on the widget area. Contains a list of clickable widgets which
// can be added to the main content area.
//

.widget-list

  .more-widgets-list
    display inline

  .widget-list-item
    display inline-block
    position relative
    list-style none
    margin 5px 5px 0 0
    padding rem(3) rem(9) rem(3) rem(6)
    color $color--white
    background-color $color--grey--light-mid
    cursor pointer
    line-height rem(18)
    border-radius 3px
    border 1px solid $color--grey--mid
    text-shadow 0 1px 2px $color--grey--mid

    .icon
      margin-top 2px
      margin-right rem(4)

  .btn
    vertical-align baseline
    padding rem(3) rem(9) rem(3) rem(6)


//
// 2. WIDGET ROW
// A row containing one or more droppable widget areas.
//

.widget-row
  position relative
  background-color $widget-row--background
  padding 10px
  box-shadow 0 0 3px rgba($color--pure-black, 0.15) inset

  .grid
    margin-left -10px

  .grid__item
    padding-left 10px
    // margin-left -1px

    &:first-child
      margin-left 0

  .panel-content
    padding 10px

  &.ui-sortable-placeholder
    margin-bottom 20px

// 'x' remove button in top right corner of row
.widget-row-remove
  position absolute
  right 0
  top 0
  margin-right -12px
  margin-top -12px

  width 24px
  height 24px
  background-color $color--black
  border 2px solid $color--white
  border-radius 40px
  box-shadow 0 1px 3px rgba($color--pure-black, 0.3)
  cursor pointer

  &:hover
  &:active
    width 28px
    height 28px
    margin-right -14px
    margin-top -14px

  &:focus
    outline 0

  .icon
    vertical-align bottom

.ui-sortable
  clearfix()

.widget-grid__item
  margin 5px 0 10px

  &.ui-sortable-placeholder-custom
    margin 5px 0 10px


// Placeholder appears when dragging new, or reordering existing widgets
.ui-sortable-placeholder
  margin-bottom 20px
.ui-sortable-placeholder-custom
  visibility visible !important //@stylint ignore
  list-style none
  margin-left 0
  background-color $ui-sortable-placeholder--background
  box-shadow none
  border 0
  margin-bottom -1px // Nasty fix to avoid oversized placeholders

  // height 282px
  // opacity 1
  // min-height 32px
  // margin 5px 0px
  // box-sizing border-box
  // clear both

// Illustrates an area where widgets can be dropped
.widget-drop-area
  padding 10px
  border 1px dashed $widget-drop-area--border
  background-color $widget-drop-area--background

// An unillustrated area which can contain which widgets,
// but can not have widgets dropped on it. It *must not*
// have one of the following properties, otherwise it breaks
// the jQuery UI sortable functionality:
//   overflow: hidden
//   float: left
.widget-beancan-area
  clearfix()
  margin-bottom 20px
  .widget-grid__item
    // float left
    display inline-block
    width 20%
    vertical-align top

    .list-item-header
      .list-item-actions
      .control-group
        margin-left 0

  .widget
    margin 5px

//
// 3. WIDGET
// Single draggable widgets, made up of a header, and collapsable content area.
//


.widget
  @extend .list-item //@stylint ignore - list item refactor will make this obsolete
  margin 10px 0

  iframe
    border 1px solid $color--border
    margin-left -1px
    margin-right -1px
    width 100%

  h2
    vertical-align middle

  .html-description
    margin-bottom 10px

  .list-item-actions
    float right
    margin-left 10px

    .control-group
      display inline-block
      vertical-align top

    .label
      vertical-align top

  .list-item-header
    clearfix()

  .list-item-header + .list-item-content
    border-top 1px solid $color--border


// When a widget lives in a thin column, drop the actions below the title.
.one-third
  .widget
    .list-item-actions
      clear left
      float left
      margin-left 0
      margin-top 10px
      .btn
        margin-left 0
        margin-right 10px
      .btn-group
        margin-left 0
        margin-right 10px
        .btn
          margin-right -1px
      .label
        vertical-align top
        margin-left 0
        margin-right 10px
