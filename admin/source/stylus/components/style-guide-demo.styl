//
// STYLE GUIDE DEMO
// ================
// Additional styles to allow static versions of dynamic features to display
// correctly within the Style Guide
//


.style-guide-demo
  padding ($base--spacing / 2)
  border 1px solid $color--border
  background-color $color--grey--light
  margin-bottom $base--spacing
  border-radius $base--border-radius

  .anytime-picker
    position static

  .foreground-notification
    position static
    display inline-block
    margin-top 0
    margin-left 0
    margin-right ($base--spacing / 2)

  .tooltip
    position relative
    display inline-block
    opacity 1

  .tooltip + .tooltip
    margin-left $base--spacing
