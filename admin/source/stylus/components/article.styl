//
// ARTICLE
// ==============
// All styles which are specific to articles and the article picker
//

.fixed-height
  height 400px
  overflow-y auto

.syndicate-instance-select
  margin-top ($base--spacing / 2)
  margin-bottom ($base--spacing / 2)
  max-width 300px
  margin-left auto

.restore-original-btn
  transition all 0.3s ease

  &:hover
    width 100px !important

.js-ai-model-select--writer,
.js-ai-model-select--editor
  position relative
  
  .selectize-input
    padding-left 85px // Space for label
    padding-right 30px
    
    &::before
      position absolute
      left 8px
      top 50%
      transform translateY(-50%)
      font-size 0.8em
      color #666 //@stylint ignore
      font-weight 600
      padding-right 8px
      border-right 1px solid #ddd //@stylint ignore
      
.js-ai-model-select--writer
  .selectize-input::before
    content 'Writer'

.js-ai-model-select--editor
  .selectize-input::before
    content 'Editor'

.model-selects
  display flex
  flex-direction column
  margin-top ($base--spacing / 2)
  margin-bottom ($base--spacing / 2)
  gap ($base--spacing / 4)
  position relative
  padding-right 75px

  .ai-model-select
    width 400px

  &::before
    content 'Experimental'
    position absolute
    right 0
    top 50%
    transform translateY(-50%)
    background-color #ffcc00 //@stylint ignore
    color #000 //@stylint ignore
    padding 5px 10px
    border-radius 3px
    font-weight bold
    font-size 0.5em
    height 100%


  & > div
    border 2px solid orange

