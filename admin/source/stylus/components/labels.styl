//
// LABELS
// ======
//


//
// BASE STYLES
//

.label
  display inline-block
  padding 2px 4px
  font-size 12px
  line-height 12px
  color $color--white
  vertical-align baseline
  white-space nowrap
  background-color $color--default
  border-radius $base--border-radius
  cursor default

  &:empty
    display none

  // TODO: Remove after spaced-group refactor
  & + &
    margin-left rem(10)
  .spaced-group--condensed & + &
    margin-left rem(5)

//
// STATE MODIFIERS
//

for $type in 'standard' 'success' 'notice' 'warning' 'error' 'inverse' 'live'
  $color = lookup('$color--' + $type)
  .label--{$type}
    background-color $color


//
// SIZE MODIFIERS
//

.label--small
  padding 2px 4px
  font-size rem(12)
  line-height rem(12)

.label--large
  padding 5px
  font-size rem(14)
  line-height rem(14)

.label--xlarge
  padding 10px
  font-size rem(14)
  line-height rem(14)


//
// CUSTOM CONTENT
//

.label--plain
  color $color--black
  border 1px solid shade($color--grey--light, 10%)
  background-color $color--grey--light
  padding-top 0
  padding-bottom 0

  .control--boolean
    vertical-align top
    margin-top 1px
    margin-right 0

  &.label--large
    padding-top 4px
    padding-bottom 4px

  &.label--xlarge
    padding-top 9px
    padding-bottom 9px
