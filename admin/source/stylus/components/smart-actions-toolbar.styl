//
// SMART ACTIONS TOOLBAR
// ====================
// Collapsible smart actions component for the article toolbar
//

.smart-actions-toolbar-component
  position relative
  display inline-block

.smart-actions-collapsed
  .loading-state
    display flex
    align-items center
    gap 8px //@stylint ignore
    color white //@stylint ignore
    font-size 12px
    
    .spinner
      width 16px
      height 16px
      border 2px solid #28a745 //@stylint ignore
      border-top 2px solid #666 //@stylint ignore
      border-radius 50%
      animation spin 1s linear infinite
      
    @keyframes spin
      0% { transform: rotate(0deg) }
      100% { transform: rotate(360deg) }

  .loaded-state
    .js-smart-actions-toggle
      position relative
      display flex
      align-items center
      gap 6px  //@stylint ignore
      border 1px solid #ddd //@stylint ignore
      background #fff //@stylint ignore
      border-radius 4px
      cursor pointer
      font-size 12px
      transition all 0.2s ease
      
      &:hover
        background #f8f9fa //@stylint ignore
        border-color #ccc //@stylint ignore
        
      &.active
        background #007bff //@stylint ignore
        color white //@stylint ignore
        border-color #007bff //@stylint ignore
        
      &.btn--action
        background #28a745 //@stylint ignore
        color white //@stylint ignore
        border-color #28a745 //@stylint ignore
        
        &:hover
          background #218838 //@stylint ignore
          border-color #1e7e34 //@stylint ignore
          
      &.btn--disabled
        background #f8f9fa //@stylint ignore
        color #6c757d //@stylint ignore
        border-color #dee2e6 //@stylint ignore
        cursor not-allowed
        
        &:hover
          background #f8f9fa //@stylint ignore
          border-color #dee2e6 //@stylint ignore

      .icon
        font-size 14px
        
      .badge
        background rgba(255, 255, 255, 0.2) //@stylint ignore
        color inherit
        padding 2px 6px
        border-radius 10px
        font-size 10px
        font-weight bold
        min-width 16px
        text-align center
        
      .caret
        margin-left 4px
        border-top 4px solid
        border-right 4px solid transparent
        border-left 4px solid transparent
        margin-top 0

.smart-actions-popup
  position absolute
  top 100%
  right 0
  width 400px
  max-width 90vw
  background white //@stylint ignore
  border 1px solid #ddd //@stylint ignore
  border-radius 6px
  box-shadow 0 4px 12px rgba(0, 0, 0, 0.15) //@stylint ignore
  z-index 1000
  margin-top 5px
  
  .popup-header
    display flex
    flex-direction column
    justify-content space-between
    align-items flex-start
    padding 16px
    border-bottom 1px solid #eee //@stylint ignore
    background #f8f9fa //@stylint ignore
    border-radius 6px 6px 0 0
    
    h3
      margin 0
      font-size 16px
      font-weight 600
      color #333 //@stylint ignore
      
    .subtitle
      font-size 12px
      color #666 //@stylint ignore
      margin-top 4px
      text-align left
      text-wrap auto  //@stylint ignore
      margin-top 8px
      margin-bottom 4px
      
    .js-close-popup
      background none
      border 0
      font-size 18px
      color #999 //@stylint ignore
      cursor pointer
      padding 0
      width 24px
      height 24px
      display flex
      align-items center
      justify-content center
      border-radius 4px
      
      &:hover
        background #e9ecef //@stylint ignore
        color #666 //@stylint ignore

  .popup-content
    max-height 400px
    overflow-y auto
    
    .no-actions
      padding 32px 16px
      text-align center
      color #666 //@stylint ignore
      font-style italic
      
  .popup-footer
    padding 12px 16px
    border-top 1px solid #eee //@stylint ignore
    background #f8f9fa //@stylint ignore
    border-radius 0 0 6px 6px
    
    .actions-summary
      font-size 12px
      color #666 //@stylint ignore
      text-align center

.popup-action-item
  padding 12px 16px
  border-bottom 1px solid #f0f0f0 //@stylint ignore
  
  &:last-child
    border-bottom none
    
  &.type\:uncreated
    border-left 4px solid #28a745 //@stylint ignore
    
  &.type\:unlinked
    border-left 4px solid #6f42c1 //@stylint ignore
    
  &.type\:linked
    border-left 4px solid #6c757d //@stylint ignore
    opacity 0.7

  .item-header
    display flex
    justify-content space-between
    align-items flex-start
    margin-bottom 8px
    
    .company-name
      font-weight 600
      color #333 //@stylint ignore
      font-size 14px
      
    .item-badges
      display flex
      gap 4px  //@stylint ignore
      flex-wrap wrap
      
      .badge
        font-size 10px
        padding 2px 6px
        border-radius 10px
        font-weight 500
        
        &.badge--tagged
          background #e3f2fd //@stylint ignore
          color #1976d2 //@stylint ignore
          
        &.badge--mongo
          background #e8f5e8 //@stylint ignore
          color #2e7d32 //@stylint ignore
          
        &.badge--linked
          background #f3e5f5 //@stylint ignore
          color #7b1fa2 //@stylint ignore

  .item-content
    margin-bottom 12px
    
    .status-description
      font-size 13px
      color #555 //@stylint ignore
      margin-bottom 4px
      
    .rationale-text
      font-size 11px
      color #777 //@stylint ignore
      background #f8f9fa //@stylint ignore
      padding 4px 8px
      border-radius 4px
      margin-bottom 2px
      border-left 3px solid #dee2e6 //@stylint ignore
      text-wrap auto  //@stylint ignore

  .item-action
    .action-btn
      padding 6px 12px
      border-radius 4px
      font-size 12px
      font-weight 500
      cursor pointer
      border 0
      transition all 0.2s ease
      
      &.btn-green
        background #28a745 //@stylint ignore
        color white //@stylint ignore
        
        &:hover
          background #218838 //@stylint ignore
          
        &:disabled
          background #6c757d //@stylint ignore
          cursor not-allowed
          
      &.btn-purple
        background #6f42c1 //@stylint ignore
        color white //@stylint ignore
        
        &:hover
          background #5a32a3 //@stylint ignore
          
        &:disabled
          background #6c757d //@stylint ignore
          cursor not-allowed
          
      &.btn-purple-outline
        background transparent
        color #6f42c1 //@stylint ignore
        border 1px solid #6f42c1 //@stylint ignore
        
        &:hover
          background #6f42c1 //@stylint ignore
          color white //@stylint ignore

// Responsive adjustments
@media (max-width: 768px)
  .smart-actions-popup
    width 320px
    
    .popup-action-item
      .item-header
        flex-direction column
        align-items flex-start
        gap 8px  //@stylint ignore
        
      .item-badges
        align-self flex-start
