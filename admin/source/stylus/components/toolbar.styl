//
// TOOLBAR
// =======
// Fixed above the page content. Contains page / section specific actions.
//


$toolbar--background = #333

.toolbar
  position fixed
  top 0
  right 0
  text-align right
  background-color $toolbar--background
  z-index zi('toolbar')
  padding-top 10px
  padding-bottom 10px
  left $sidebar-width
  +width-min($mq--desktop)
    left $sidebar-width--desktop
  .touchevents &
    left $sidebar-width--desktop

  .page-content .page-split__flexi &
    // padding-left 400px

  .btn
    margin-left 10px
    vertical-align top


  .control-group
    display inline-block
    margin-left 20px
    vertical-align top

.toolbar__left
  float left
  .btn
    margin-left 0
    margin-right 10px
  .btn-group .btn
    margin-right 0
  .control-group
    margin-left 0
    margin-right 20px
