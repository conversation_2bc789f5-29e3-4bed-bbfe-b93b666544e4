//
// DROPDOWNS
// =========
//


.dropup
.dropdown
  position relative

.dropdown-toggle:active
.open .dropdown-toggle
  outline 0

.caret
  content ''
  display inline-block
  width 0
  height 0
  vertical-align top
  border-top 4px solid $color--black
  border-right 4px solid transparent
  border-left 4px solid transparent

.dropdown .caret
  margin-top 8px
  margin-left 2px

.dropup .caret
  border-top 0
  border-bottom 4px solid $color--black
  content ''

.dropdown-menu
  display none
  position absolute
  top 100%
  left 0
  float left
  min-width 160px
  padding rem(4) 0
  margin rem(1) 0 0
  list-style none
  background-color $color--white
  border 1px solid shade($color--grey--light, 10%)
  border-radius $base--border-radius
  box-shadow 0 3px 6px rgba($color--black, 0.2)
  background-clip padding-box
  text-align left
  z-index zi('dropdown-menu')

  .open > &
    display block

  .pull-right > &
    right 0
    left auto

  .dropup &
    top auto
    bottom 100%
    margin-bottom 1px

  > li
    list-style none
    margin-left 0

  &.pull-right
    right 0
    left auto

  .divider
    height 1px
    margin rem(4) 0
    overflow hidden
    background-color shade($color--grey--light, 10%)

  > li > a
    display block
    padding rem(3) rem(10)
    clear both
    font-weight normal
    line-height rem(20)
    color $color--black
    white-space nowrap
    text-decoration none

    &:hover
    &:focus
      text-decoration none
      background-color $color--grey--light

  > .disabled > a
  > .disabled > a:hover
  > .disabled > a:focus
    color $color--disabled
    background-color transparent
    cursor default
