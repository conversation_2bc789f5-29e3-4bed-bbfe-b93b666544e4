//
// PAGE SIDEBAR
// ============
//

// TODO - review this as part of a larger responsive refactor

.page-sidebar
  //

.page-sidebar__toggle
  height 100%
  width 100%
  background-color $color--white
  border 0
  display block
  vertical-align top

  +width-min($mq--desktop)
    display none

  .page-sidebar__toggle__header
    width 100%
    height 48px
    line-height 48px
    background-color $color--primary
    vertical-align top
    text-align center

    .icon
      vertical-align middle

.page-sidebar__content
  display none

  +width-min($mq--desktop)
    display block

.touchevents .page-split__left
.page-split__left:hover
  width $sidebar-width--desktop
  z-index zi('page-sidebar-hover')
  .page-sidebar__toggle
    display none
  .page-sidebar__content
    display block
