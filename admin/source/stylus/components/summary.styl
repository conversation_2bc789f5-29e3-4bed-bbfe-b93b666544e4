//
// SUMMARY
// =======
//


//
// BASE STYLES
//

.summary
  position relative
  list-style none
  margin-left 0
  margin-bottom ($base--spacing / 2)
  background-color $color--white
  border 1px solid $color--border
  box-shadow 0 1px 2px rgba($color--pure-black, 0.1)


//
// ELEMENTS
//

.summary__header
  clearfix()
  padding rem(10)


.summary__title
  margin-bottom 0
  font-size rem(17)
  line-height 24px // Using rem() here causes 24 to round down to 23 in Chrome.
  font-weight $font-weight--normal
  a
    ellipsis()
    display block
    text-decoration none


.summary__affix

  > .btn
  > .btn-group
    vertical-align top

.summary__affix--prefix
  float left
  margin-right rem(10)

  & + .summary__affix--suffix
    margin-left 0

.summary__affix--suffix
  float right
  margin-left rem(10)


.summary__asset
  position relative
  background-repeat repeat
  background-position 0 0
  background-image versionPath('/assets/img/content/checkerboard-bg.gif')

.summary__image
  display block
  position absolute
  left 0
  top 0
  bottom 0
  width rem(80)
  border-right 1px solid $color--border
  z-index 1

  background-color $color--grey--light
  background-position 50% 25%
  background-size cover
  background-repeat no-repeat

  + .summary__header
  + .summary__header + .summary__content
    margin-left rem(80)

  img
    position absolute
    top 0
    right 0
    bottom 0
    left 0
    margin auto
    max-width 90%
    max-height 90%

.summary__content
  padding rem(10)
  color $color--grey--mid
  font-size rem(13)
  line-height 1.4

  .summary__header + &
    border-top 1px solid $color--border

  dt
    color tint($color--grey--mid, 30%)
    font-weight normal

  hr
    margin-top rem(10)
    margin-bottom rem(10)

  iframe
    width 100%
    margin-left -1px
    margin-right -1px
    border 1px solid $color--border

  dl, p
    margin-bottom 0
    & + iframe
    & + .html-editor
      margin-top rem(5)


//
// MODIFIERS
// =========
//

.summary--condensed

  .summary__header
    padding rem(5)

  .summary__title
    font-size rem(14)

  .summary__affix--prefix
    margin-right rem(5)

    & + .summary__affix--suffix
      margin-left 0

  .summary__affix--suffix
    margin-left rem(5)
