//
// CONTROL ADDON
// =============
// Allows labels, buttons, booleans etc to be prefixed or suffixed to a control
//


.control-addon
  width 100%
  display table

.control-addon__base
  // Ensure box-shadow from base elements appears above suffix
  position relative
  z-index 1

.control-addon__affix
  display table-cell
  width 1%
  vertical-align top
  white-space nowrap

  // TODO: remove this once form overhaul has happened.
  // Temporarily required to overcome specificity issues
  .control--boolean
    margin 1px 0 0 !important //@stylint ignore

  > .label
  > .btn
    display block

  > .label
    border-radius 0

.control-addon__affix--prefix
  > .label
  > .btn
    margin-right 5px

  &.control-addon__affix--attached
    > .label
    > .btn
      margin-right -1px
      border-top-right-radius 0
      border-bottom-right-radius 0

.control-addon__affix--suffix
  > .label
  > .btn
    margin-left 5px
  &.control-addon__affix--attached
    > .label
    > .btn
      margin-left -1px
      border-top-left-radius 0
      border-bottom-left-radius 0
