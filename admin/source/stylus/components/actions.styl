//
// FORM ACTIONS
// ============
// Used to layout form actions, with optional left/right alignment
//


.form-actions
  //

.form-actions__primary
.form-actions__secondary

  > .btn
    vertical-align baseline

.form-actions__primary
  margin-bottom 10px

+width-min($mq--tablet)
  .form-actions
    text-align right

  .form-actions--split
    display table
    width 100%

  .form-actions__primary
  .form-actions__secondary
    display table-cell
    margin-bottom 0
    vertical-align middle

  .form-actions__primary
    text-align left
    margin-bottom 0
