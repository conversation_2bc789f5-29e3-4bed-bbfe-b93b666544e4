//
// GLOBAL LOADER
// =============
// A loading bar which appears under the global `toolbar` when required.
//

$global-loading--base = #006dcc
$global-loading--base-light = #7fb3db
$global-loading--base-dark = #004c8e

.global-loading
  height 0
  transition height 0.3s ease
  transition-delay 0.3s
  position relative
  top 10px // TODO Temp
  width 100%
  background-color $global-loading--base
  background-image linear-gradient(to right, $global-loading--base-light 20%, $global-loading--base 50%, $global-loading--base-light 80%)
  background-repeat repeat
  background-size 1000px 100%
  animation-name global-loading
  animation-duration 1s
  animation-iteration-count infinite
  animation-timing-function linear
  box-shadow 0 0 2px rgba($color--black 0.5) inset

  &:before
  &:after
    content ''
    position absolute
    height 5px
    left 0
    width 100%

  &:before
    top 100%
  &:after
    bottom 100%

  &.currently-loading
    height 5px

  .global-loading-content
    opacity 0
    transition opacity 0.3s ease
    display block
    width 100%
    text-align center
    text-transform uppercase
    font-weight bold
    color $color--white
    font-size 13px
    line-height 20px
    pointer-events none
    overflow hidden
    padding-top 1px
    text-shadow 0 0 2px $global-loading--base-dark

  &:hover
    transition-delay 0
    height 20px
    background-image linear-gradient(to right, $global-loading--base-light 20%, $global-loading--base 50%, $global-loading--base-light 80%)

    .global-loading-content
      opacity 1
      transition-delay 0.3s

@keyframes global-loading
  0%
    background-position 0 0
  100%
    background-position 1000px 0
