// TODO: This file needs a full review once more functionality is in place

//
// LIST-ITEM STYLES
// ================
//
//


//
// LIST ITEM - GENERIC STYLES
//

.list-item
  position relative
  background-color $color--white
  border 1px solid $color--border
  margin-bottom ($base--spacing / 2)
  box-shadow 0 1px 2px rgba($color--pure-black, 0.1)
  list-style none
  margin-left 0


  .image
    display block
    background-color $color--base
    background-position 50% 25%
    background-size cover
    background-repeat no-repeat
    transition all 0.3s ease

.list-item-header
  padding 10px
  background-color $color--white

  h2,
  h4,
  .list-item-title
    margin-bottom 0
    font-size 1.2em
    line-height rem(24)
    font-weight 400
    a
      ellipsis()
      display block
      text-decoration none
  h4
    font-size 1.0em

  h5.list-item-title
    font-size 1.1em
    line-height 1.5em

  .label:first-child
    float left

.list-item-header + .list-item-content
  border-top 1px solid $color--border

.list-item-content
  clearfix()
  padding 5px 10px

  dl
    display inline
    margin-bottom 0

  dt
  dd
    display inline
    margin-right 4px
    margin-bottom 0
  dt
    font-weight normal
  dd
    margin-right 20px

  p
  dl
    font-size 13px
    color $color--grey--light-mid

  p
    margin-bottom 0

  .p--inline
    display inline-block
    margin-right 20px

  a
    color $color--grey--mid
    &:hover
      color $color--primary

  .list-item:last-child
  .form-row
    margin-bottom 0

// Used when a drag handle appears before a title - needs refactor
.list-item-leading-actions
  margin-right 10px
  float left

.list-item-actions
  vertical-align top
  float right
  margin-left 10px

  &.list-item-actions--left
    float left
    margin-right 10px
    margin-left 0

    *:first-child
      margin-left 0

  .btn
    margin-left 10px
    vertical-align top
  .btn-group
    margin-left 10px
    .btn
      margin-left -1px
  .label
    vertical-align top
    margin-left 10px

  .list-item-select
    height 24px
    width 24px
    line-height 20px
    background-color $color--grey--light
    border 1px solid $color--border
    display inline-block
    text-align center
    border-radius 4px
    margin-left 10px
    vertical-align middle

    .form-field
    input[type='checkbox']
      vertical-align middle

  .platform
    color $color--white
    padding 2px 6px
    border-radius 2px
    margin-left 10px
    vertical-align middle

    &.platform--com
      background $color--primary

    &.platform--io
      background $color--io


//
// LIST ITEM - DETAIL VIEW
//

.list-item-detail

  padding-left 80px

  .image-detail
    position absolute
    left 0
    top 0
    bottom 0
    width 80px
    z-index 1
    border-right 1px solid $color--border

  .image-corner
    position absolute
    left 0
    top 0
    bottom 0
    width 20px
    height 20px
    z-index 2
    border-right 1px solid $color--border

  .image-logo
    position absolute
    top 0
    right 0
    bottom 0
    left 0
    margin auto
    max-width 90%
    max-height 90%

  .list-item-schedule
    float right
    margin-right 0
    dt
      margin-left 20px
    dd
      margin-right 0


//
// LIST ITEM - THUMBNAIL VIEW
//

.list-item-thumbnail

  .list-item-inner
    height 345px
    overflow hidden

  .image-wrapper
    position relative
    background-image versionPath('/assets/img/content/checkerboard-bg.gif')
    background-repeat repeat

  .image
    background-color transparent

  .image-thumbnail
    max-width 235px
    height 235px
    padding-bottom 75px // Minimum height of .list-item-header
    width 100%
    cursor pointer

  &:hover
    .image-transition
      height 130px

  &.is-scheduled:hover
    .image-thumbnail
      height 115px

  .list-item-header
    position absolute
    bottom 0
    padding 0
    min-height 75px
    width 100%
    border-top 1px solid $color--base
    border-bottom 1px solid $color--base

    h2
      margin 10px
      display block

  .list-item-actions
    position absolute
    bottom 0
    left 0
    right 0
    padding 5px 10px
    background-color $color--white
    border-top 1px solid $color--base
    text-align right
    float none
    margin-left 0

    .label
      float left
      margin-left 0

    .list-item-select
      float left
      margin-left 0
      margin-right 5px

  .list-item-content
    opacity 0
    transition opacity 0.3s ease

    dl
      display block
      margin-right 0
    dt
      clear left
      float left
      width 30%
      margin-right 0
      display block
    dd
      display block
      margin-bottom 0
      margin-right 0

  &:hover
    .list-item-content
      opacity 1


.list-item-thumbnail-short
  height 157px

  .image-thumbnail
    height 120px
    padding-bottom 0

  .list-item-header
  .list-item-content
    display none

  .list-item-actions
    padding 5px


// Only used for tags list page
.list-item--condensed
  .list-item-header
    padding 5px 5px 5px 10px
    h2
      font-size rem(12px)
      a
        display inline
  .list-item-actions
    margin-right 0
    margin-left 0

.instance-logo
  margin-left 10px
  img
    border-radius 3px
    height 23px


// LIST ITEM - INVERSE
.list-item--inverse
  background-color rgba(#000, 1) //@stylint ignore
  color $color--white

  a, p
    color $color--white

  .list-item-header
    background-color rgba(#000, 1) //@stylint ignore
    color $color--white