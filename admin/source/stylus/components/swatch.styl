//
// SWATCH
// ======
// Form Row which contains 2x colour picker and 1 swatch preview.
//


.swatch
  display block
  width 100%
  height 34px

  background-color $color--white
  background-image versionPath('/assets/img/content/checkerboard-bg.gif')
  background-position 1px 1px
  background-repeat repeat

.swatch__input
  &.is-invalid
    border-color $color--error
    &:focus
      border-color $color--error
      box-shadow 0 0 5px rgba($color--error, 0.8)

.swatch__preview
  border 1px solid $color--border
  padding 5px 10px
  color transparent
  cursor default

  &.has-contrast-warning
    border-color $color--warning
    box-shadow 0 0 5px rgba($color--warning, 0.8)
    cursor help
