.suggestions-header
  display flex
  justify-content space-between
  align-items flex-start
  gap $base--spacing  //@stylint ignore
  flex-direction column

  +width-min($mq--desktop)
    flex-direction row


.suggestions-title
  display block !important
  margin-bottom $base--spacing !important
  position relative
  padding-left 45px

  &::before
    content 'v1'
    position absolute
    left 0
    top 50%
    transform translateY(-50%)
    background-color $color--green //@stylint ignore
    color #000 //@stylint ignore
    padding 5px 10px
    border-radius 3px
    font-weight bold
    font-size 0.5em

.suggestions-tip
  background $color--grey--light
  display inline

.suggestions-actions
  border 1px solid $color--grey
  padding ($base--spacing / 4)
  background $color--whitesmoke
  flex-shrink 0
  *
    margin-left 0 !important
    border-radius 0

  +width-min($mq--desktop)
    order 2

.suggestion-list
  padding-bottom $base--spacing
  min-height 200px
  max-height 600px
  overflow-y scroll

  &.hidden
    opacity 0
    display none

  &.loading
    .suggestion-wrapper
      opacity 0

.suggestion-wrapper
  display flex
  align-items center
  gap ($base--spacing * 2)
  padding $base--spacing ($base--spacing / 4)
  border-bottom 1px solid $color--grey--light
  transition opacity 1s ease-in-out
  border-radius 4px

  &.accepted
    opacity 0.35

  &:hover
    background rgba($color--primary, 0.1)

.suggestions-label
  background rgba($color--green, 0.75) //@stylint ignore
  margin-bottom $base--spacing
  padding-left ($base--spacing / 4)
  padding-right ($base--spacing / 4)
  padding-top ($base--spacing / 8)
  padding-bottom ($base--spacing / 8)

.article-wrapper
  width 100%

.suggestion-top
  display flex
  justify-content space-between
  align-items center
  margin-bottom $base--spacing

.suggestions-trigger
  margin-bottom $base--spacing

.actions-wrapper
  margin-left auto

.btn.accept, .btn.accepted
  background $color--white
  border 1px solid $color--green
  color $color--green

  &:hover,
  &:active,
  &:focus
    background $color--green
    color $color--white

.btn.deny
  background $color--white
  border 1px solid $color--red
  color $color--red

  &:hover,
  &:active,
  &:focus
    background $color--red
    color $color--white

.article-title
  font-weight bold
  margin-bottom 0 !important

.article-date
  margin-bottom 0 !important

.snippet
  .target
    font-weight bold
    color $color--blue
    text-decoration underline

.btn.hidden
  display none

.suggestion-url-wrapper
  display flex
  width max-content
  font-weight bold
  font-size 1em
  margin-top $base--spacing

  &.suggestion-url-wrapper--blue
    background rgba($color--blue, 0.1)

  .suggestion-url
    display inline
    margin-right ($base--spacing / 8)

  .suggestion-link
    color $color--blue

.search-wrapper
  display flex
  align-items center
  gap ($base--spacing / 2)
  margin-top $base--spacing

  &.hidden
    display none

.search-additional-fields
  position relative
  margin-top ($base--spacing * 2)
  display grid
  justify-content flex-start
  gap ($base--spacing / 2)
  margin-bottom ($base--spacing / 2)

  input
    min-width 200px
  
  .form-row
    max-width 550px
    margin-bottom 0

  &::before
    content '*Less than your limit value may be returned'
    font-size 0.8em
    margin-left auto
    position absolute
    top ($base--spacing * -1)
    font-weight bold

.search-additional-fields-label
  flex-shrink 0
  background-color $color--grey--light

  color #000 //@stylint ignore
  padding 4px 8px
  border-radius 3px
  font-weight bold
  font-size 1em

