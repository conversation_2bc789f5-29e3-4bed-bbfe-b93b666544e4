//
// NOTIFICATIONS
// =============
//


//
// BASE STYLES
//

.notification
  position relative
  padding rem(10) rem(15)
  margin-bottom $base--spacing
  border-radius $base--border-radius
  color $color--black
  border 1px solid shade($color--grey--light, 10%)
  background-color $color--grey--light

  p
  ul
  ol
    margin rem(5) 0 0
    &:first-child
      margin-top 0

  hr
    margin rem(10) 0

//
// STATE MODIFIERS
//

for $type in 'action' 'success' 'notice' 'warning' 'error'
  $color = lookup('$color--' + $type)

  .notification--{$type}
    color $color--white
    background-color $color
    border-color shade($color, 10%)
    text-shadow 0 1px 2px shade($color, 30%)
    a
      color $color--white


//
// DISMISSABLE
// Add the modifier class to ensure content doesn’t clash with the button
//

.notification--dismissible
  padding-right rem(40)

.notification__dismiss
  position absolute
  right rem(10)
  top 50%
  transform translateY(-50%)
  padding 0 rem(5)

  &:hover
  &:active
    opacity 0.6
  &:focus
    outline 0
