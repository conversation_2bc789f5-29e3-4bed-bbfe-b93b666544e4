//
// FOREGROUND NOTIFICATION
// =======================
//

.foreground-notification
  position fixed
  top 50%
  left 50%
  width 150px
  margin-top -100px
  margin-left -75px
  padding 20px
  text-align center
  background-color rgba($color--black, 0.7)
  transition-property opacity, transform
  transition-duration 250ms
  transition-timing-function cubic-bezier(0.6, 1.89, 0.6, 0.72)
  opacity 1
  transform scale(1)
  border-radius $base--border-radius
  z-index zi('foreground-notification')
  filter blur(0)

  &.is-hidden
    opacity 0
    transform scale(2)
    transition-timing-function ease-out
    filter blur(5px)

  span
    display block
    line-height 42px
    margin-bottom 20px

  i
    transform scale(3)
    vertical-align middle

  p
    margin-bottom 0
    color $color--white
    font-size 20px
    line-height 1
