//
// STATS BLOCK
// ===========
// Displays a large number, over a title, with optional actions.
//


.stats-block-group
  position relative
  text-align center
  margin-bottom $base--spacing

  &:before
  &:after
    display none
    content ''
    position absolute
    top 0
    right 0
    bottom 0
    left 0
    opacity 0
    background rgba($color--white, 0.6)
    transition opacity 1s ease
    z-index 1

  &:after
    content 'Updating…'
    width 100px
    height 40px
    margin auto
    color $color--white
    border-radius 3px
    line-height 40px
    font-weight bold
    background-color rgba($color--black, 0.75)

  &.is-updating
    &:before
    &:after
      opacity 1
      transition-delay 1s
      display block


.stats-block
  display inline-block
  margin-left $base--spacing
  margin-right $base--spacing
  text-align center
  vertical-align top

.stats-block__value
  font-size 24px
  line-height 1
  font-weight bold
