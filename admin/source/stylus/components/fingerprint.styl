//
// FINGERPRINT STYLES
// =================
//

// Option 1: List-based
.fingerprint-list
  margin 0
  padding 0
  list-style none

  &__item
    display inline-block
    background $color--whitesmoke
    border 1px solid $color--border
    border-radius $base--border-radius
    padding 0.3em 0.6em
    margin 0 0.5em 0.5em 0
    font-family $font--monospace
    font-size rem(12)
    color $color--grey--dark

// Option 2: Paragraph-based
.fingerprint
  font-family $font--monospace
  font-size rem(12)
  line-height 1.6
  background $color--whitesmoke
  border 1px solid $color--border
  border-radius $base--border-radius
  padding 0.8em 1em
  color $color--grey--dark
  word-break break-all

.fingerprinted-list-content
  background $color--whitesmoke
  border 1px solid $color--border
  border-radius $base--border-radius
  margin-bottom ($base--spacing * 4)
  
  .fingerprinted-list-inner
    padding spacing(1)
    
    // When empty
    &:empty
      min-height 100px
      background-image linear-gradient(45deg, $color--border 25%, transparent 25%), linear-gradient(-45deg, $color--border 25%, transparent 25%)
      background-size 20px 20px
      background-color $color--white
      opacity 0.5
      
    // Grid layout for items
    .js-items
      > *
        animation fadeIn 0.3s ease-in-out

@keyframes fadeIn
  from
    opacity 0
    transform translateY(10px)
  to
    opacity 1
    transform translateY(0)
