//
// THIS IS A GENERATED FILE
// Any changes made to this file will be automatically overridden.
//

$icon--archived =
  background-position (-72 - 2)px (-84 - 2)px
  width 14px
  height 14px

$icon--automatic =
  background-position (-18 - 2)px (-66 - 2)px
  width 14px
  height 14px

$icon--calendar =
  background-position (-28 - 2)px (0 - 2)px
  width 17px
  height 16px

$icon--clock =
  background-position (-20 - 2)px (-28 - 2)px
  width 14px
  height 14px

$icon--create =
  background-position (-49 - 2)px (0 - 2)px
  width 14px
  height 14px

$icon--cross =
  background-position (-49 - 2)px (-18 - 2)px
  width 14px
  height 14px

$icon--cross--dark =
  background-position (0 - 2)px (-48 - 2)px
  width 14px
  height 14px

$icon--delete =
  background-position (-18 - 2)px (-48 - 2)px
  width 14px
  height 14px

$icon--download =
  background-position (-36 - 2)px (-48 - 2)px
  width 14px
  height 14px

$icon--download--dark =
  background-position (-67 - 2)px (0 - 2)px
  width 14px
  height 14px

$icon--draft =
  background-position (-67 - 2)px (-18 - 2)px
  width 14px
  height 14px

$icon--hamburger =
  background-position (-67 - 2)px (-36 - 2)px
  width 14px
  height 14px

$icon--hamburger--large =
  background-position (0 - 2)px (-66 - 2)px
  width 14px
  height 14px

$icon--important =
  background-position (-103 - 2)px (0 - 2)px
  width 14px
  height 14px

$icon--info =
  background-position (-36 - 2)px (-66 - 2)px
  width 14px
  height 14px

$icon--manual =
  background-position (-54 - 2)px (-66 - 2)px
  width 14px
  height 14px

$icon--plus =
  background-position (-85 - 2)px (0 - 2)px
  width 14px
  height 14px

$icon--published =
  background-position (-85 - 2)px (-18 - 2)px
  width 14px
  height 14px

$icon--question =
  background-position (-103 - 2)px (-18 - 2)px
  width 8px
  height 12px

$icon--save =
  background-position (-85 - 2)px (-54 - 2)px
  width 14px
  height 14px

$icon--save--dark =
  background-position (0 - 2)px (-84 - 2)px
  width 14px
  height 14px

$icon--success =
  background-position (-18 - 2)px (-84 - 2)px
  width 14px
  height 14px

$icon--tag-general =
  background-position (-36 - 2)px (-84 - 2)px
  width 14px
  height 14px

$icon--tag-instance =
  background-position (-54 - 2)px (-84 - 2)px
  width 14px
  height 14px

$icon--tag-person =
  background-position (0 - 2)px (-28 - 2)px
  width 16px
  height 16px

$icon--tag-system =
  background-position (-85 - 2)px (-36 - 2)px
  width 14px
  height 14px

$icon--warning =
  background-position (0 - 2)px (0 - 2)px
  width 24px
  height 24px


.icon--archived
  {$icon--archived}

.icon--automatic
  {$icon--automatic}

.icon--calendar
  {$icon--calendar}

.icon--clock
  {$icon--clock}

.icon--create
  {$icon--create}

.icon--cross
  {$icon--cross}

.icon--cross--dark
  {$icon--cross--dark}

.icon--delete
  {$icon--delete}

.icon--download
  {$icon--download}

.icon--download--dark
  {$icon--download--dark}

.icon--draft
  {$icon--draft}

.icon--hamburger
  {$icon--hamburger}

.icon--hamburger--large
  {$icon--hamburger--large}

.icon--important
  {$icon--important}

.icon--info
  {$icon--info}

.icon--manual
  {$icon--manual}

.icon--plus
  {$icon--plus}

.icon--published
  {$icon--published}

.icon--question
  {$icon--question}

.icon--save
  {$icon--save}

.icon--save--dark
  {$icon--save--dark}

.icon--success
  {$icon--success}

.icon--tag-general
  {$icon--tag-general}

.icon--tag-instance
  {$icon--tag-instance}

.icon--tag-person
  {$icon--tag-person}

.icon--tag-system
  {$icon--tag-system}

.icon--warning
  {$icon--warning}

