//
// SETTINGS
// ========
// Project specific setings which can be changed to modify the basic look and
// feel of the project with little effort.
//
// 1. BREAKPOINTS AND MEDIA QUERIES
// 2. COLOURS
// 3. TYPEFACES
// 4. BASE STYLES
// 5. GRID
// 6. Z-INDEX
// 7. DEBUG STYLING
// 8. DEFAULT ENVIRONMENT
//


//
// 1. BREAKPOINTS AND MEDIA QUERIES
// Setup standard widths to pass to media queries
//

$mq--mobile--portrait  = 300px
$mq--mobile--landscape = 400px
$mq--tablet--portrait  = 550px
$mq--tablet--landscape = 800px
$mq--desktop--small    = 1000px
$mq--desktop--medium   = 1280px
$mq--desktop--large    = 1600px

// Common aliases
$mq--mobile  = $mq--mobile--portrait
$mq--tablet  = $mq--tablet--portrait
$mq--desktop = $mq--desktop--small


//
// 2. COLOURS
//

// Common Project Colours
$color--blue  = #0095da
$color--pure-black = #000
$color--grey--light-mid = #777
$color--grey--mid = #666
$color--grey--dark  = #555
$color--whitesmoke = #f8f8f8
$color--green = rgb(0, 200, 0)
$color--red = rgb(200, 0, 0)
$color--blue = rgb(0, 0, 200)

// Brand Colours
$color--black       = #323232
$color--grey        = #d0d0d0
$color--grey--light = #ececec
$color--grey--lightest = #f8f8f8
$color--white       = #ffffff
$color--primary     = #7251a1
$color--io          = #00b7eb

// Default State Colours
$color--action   = $color--primary
$color--disabled = #aaa
$color--inverse  = $color--black
$color--success  = #4caf50
$color--notice   = #4791a9
$color--warning  = #dc7f2a
$color--error    = #f44336
$color--live    = #f44336

// TODO currently only for `.labels` and `.progress` – possible refactor
$color--default  = #999
$color--standard = #006dcc

$color--base   = #bbb
$color--border = #ddd
$color--border--focus = #999


//
// 3. TYPEFACES
// Amend variable names to suit fonts used.
//

$font--open-sans = -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Helvetica Neue', Arial, sans-serif

$font-weight--normal = 400

//
// 4. BASE STYLES
//

$base--font-heading = $font--open-sans
$base--font-body    = $font--open-sans
$base--font-color   = $color--black
$base--font-size    = 14px
$base--line-height  = 1.58
$base--spacing      = rem(25)
$base--spacing--small = rem(20)
$base--body-bg      = #f8f8f8
$base--max-width    = rem($mq--desktop--large)
$base--max-width--thin = rem(600)
$base--border-radius = 3px

$sidebar-width = rem(40)
$sidebar-width--desktop = rem(200)

$form--input-padding = 5px 10px


//
// 5. GRID
//

$grid--gutter = $base--spacing


//
// 6. Z-INDEX
//

// TODO - refactor values once all z-indexes are accounted for
$z-values = {
  dropdown-menu           : 998
  anytime-picker          : 998
  tooltip                 : 5000
  foreground-notification : 1100
  toolbar                 : 999
  page-sidebar-hover      : 4000
  modal-overlay           : 9999
}


//
// 7. DEBUG STYLING
// Options are
// * error: Only critical bugs are shown
// * warning: Show error and warning bugs
// * all: Show error and warning bugs, as well as general tips / minor bugs
//

$debug--mode = false
$debug--level = 'all'


//
// 8. DEFAULT ENVIRONMENT
// Projects may optionally pass through an environment variable to dictate
// whether development specific stylesheets are compiled into the final CSS.
// Set this variable to anything other than 'development' to manually override.
//

$environment ?= $ENV || 'development'
