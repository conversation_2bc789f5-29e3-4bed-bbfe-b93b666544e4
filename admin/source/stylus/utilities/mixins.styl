zi($element)

  if type($element) != 'string'
    warn('zi() expects a string, got ' + type($element) + ' “' + $element + '”')

  else if !($element in $z-values)
    warn('Can’t find “' + $element + '” in the $z-values hash')

  else if type($z-values[$element]) != 'unit'
    warn('Value for “' + $element + '” in is a ' + type($z-values[$element]) + ' but should be a unit')

  else if unit($z-values[$element]) != ''
    warn('Value for “' + $element + '” should be a unitless number, but is in is a ' + unit($z-values[$element]) + ' unit')

  else
    $z-values[$element]
