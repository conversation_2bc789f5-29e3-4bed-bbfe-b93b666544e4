//
// INDEX STYLESHEET
// ================
// All <PERSON>yl<PERSON> requires should live in here.
//


//
// SETUP
//

@require 'stylus-mixins'
@require 'responsive-grid'
@require 'utilities/settings'
@require 'utilities/mixins'


//
// FOUNDATIONS
// Base HTML and generic site-wide elements
//

@require 'foundations/base'
@require 'foundations/typography'
@require 'foundations/visibility'
@require 'foundations/structure'
@require 'foundations/grid'
@require 'foundations/form'
@require 'foundations/controls'
@require 'foundations/fields'
@require 'foundations/icons'


//
// LAYOUT SECTIONS
//

@require 'layout/*'


//
// GENERIC COMPONENTS
//

// * doesn't seem to be working
@require 'components/*'
  
@require 'components/event'
@require 'components/suggestions'
@require 'components/smart-actions-toolbar'
@require 'components/fingerprint'
@require 'components/article'
@require 'components/auto-save'


//
// DEVELOPMENT STYLES
//

if $environment == 'development'

  @require 'dev/sandbox'

  @require 'dev/browsersync'

  // Change the $debug variable in settings.styl to activate debug mode.
  if $debug--mode == true
    @require 'dev/debug'
