const parseDomain = require('./domain-parser')

module.exports = function performLogin(data) {
  window.localStorage.setItem('apiKey', data.key)
  window.localStorage.setItem('apiId', data.id)
  window.localStorage.setItem('aclRoles', data.roles)
  window.localStorage.setItem('firstName', data.firstName)

  document.cookie = [
    'cmsLoggedIn=true',
    'path=/',
    `domain=${parseDomain(window.location.hostname)}`,
    'expires=2147483647' // End of unix time, 2038
  ].join(';')

  document.location = '/'
}
