const join = require('path').join
const compileJade = require('browjadify-compile')
const formErrorsTemplate = compileJade(join(__dirname, '/form-errors.jade'))
const formErrorTemplate = compileJade(join(__dirname, '/form-error.jade'))

function showErrors(errors) {
  this.clearErrors()

  const $errors = formErrorsTemplate({ errors: errors })
  const firstError = Object.keys(errors)[0]
  Object.keys(errors).forEach((key) => {
    this.$('[data-field=' + key + '] .js-error').append(
      formErrorTemplate({ error: errors[key] })
    )
  })
  this.$('.js-errors-summary').append($errors)
  // Set the focus to the first error
  this.$(':input[name=' + firstError + ']').focus()
}

function clearErrors() {
  this.$('.js-errors-summary, .js-error').empty()
}

module.exports = function () {
  return {
    showErrors: showErrors,
    clearErrors: clearErrors
  }
}
