const View = require('ventnor')

class ItemSelectView extends View {
  constructor(
    serviceLocator,
    {
      service,
      selectedItems = [],
      filter = {},
      multiple = true,
      singular = 'item',
      plural = 'items'
    }
  ) {
    super(serviceLocator, selectedItems)
    this.$el = $(`<select ${multiple ? 'multiple' : ''}/>`)
    this.$el.addClass('control control--choice control--multiline')
    this.el = this.$el[0]
    this.$el.attr(
      'placeholder',
      multiple ? `Choose one or more ${plural}` : `Choose an ${singular}`
    )
    this.filter = filter
    this.selectedItems = Array.isArray(selectedItems)
      ? selectedItems
      : [selectedItems]
    this.service = service
    this.singular = singular
    this.plural = plural
  }

  load(ids, cb) {
    this.service.find('', { _id: { $in: ids } }, [], {}, (err, res) => {
      if (err) return cb(err)
      return cb(null, res.results)
    })
  }

  select(ids, items) {
    const index = items.reduce((index, item) => {
      index[item._id] = item
      return index
    }, {})
    // This ensure they stay in the same order
    ids.forEach((id) => {
      // The item needs to be added to the list
      // of selectize options in order to be selected
      const item = index[id]
      const text = item ? this.getItemText(item) : id

      this.el.selectize.addOption({
        ...item,
        value: id,
        text
      })
      // Select the added option
      this.el.selectize.addItem(id)
    })
  }

  initialise() {
    this.el.selectize.on('change', this.updateSelection.bind(this))
    const selected = this.selectedItems
    this.load(selected, (err, items) => {
      if (err) return alert(`Cannot find existing ${this.plural}`)
      this.select(selected, items)
    })
  }

  reinitialise(selectedItems) {
    this.selectedItems = Array.isArray(selectedItems)
      ? selectedItems
      : [selectedItems]
    this.load(this.selectedItems, (err, items) => {
      if (err) return alert(`Cannot find existing ${this.plural}`)
      this.select(this.selectedItems, items)
    })
  }

  getItemText(item) {
    return item.name || `${item.firstName} ${item.lastName}`
  }

  updateSelection() {
    this.selectedItems = this.el.selectize.getValue()
    this.emit('change', this.selectedItems)
  }

  query(keywords, cb) {
    this.service.find(
      keywords,
      this.filter,
      [],
      { pageSize: 1000 },
      (err, data) => {
        if (err) return cb(err)
        cb(
          data.results.map((item) => ({
            ...item,
            value: item._id,
            text: this.getItemText(item)
          }))
        )
      }
    )
  }

  clear() {
    this.el.selectize.clear()
  }

  get items() {
    return this.selectedItems
  }

  getSelectedItemData() {
    if (!this.selectedItems || !this.selectedItems.length) return null
    const options = this.el.selectize.options
    const items = Array.isArray(this.selectedItems)
      ? this.selectedItems
      : [this.selectedItems]
    return items.map((item) => options[item])
  }

  render() {
    setTimeout(() => {
      this.$el.selectize({
        delimiter: ',',
        create: false,
        highlight: false,
        onInitialize: this.initialise.bind(this),
        load: this.query.bind(this),
        preload: true
      })
    }, 0)
    return this
  }
}

module.exports = ItemSelectView
