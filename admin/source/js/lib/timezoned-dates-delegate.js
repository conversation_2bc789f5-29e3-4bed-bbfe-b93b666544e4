const modal = require('modal')
var moment = require('moment-timezone')

const applyTimezone = (timezone, dateFieldMap = {}) => {
  if (!timezone)
    return [new Error('No timezone provided, model not updated'), null]

  const out = {}
  // eslint-disable-next-line
  Object.keys(dateFieldMap).forEach((key) => {
    const date = dateFieldMap[key]
    if (!date)
      return [new Error(`No date provided for ${key}, model not updated`), out]

    const convertedDate = [
      moment(date).toISOString(true).slice(0, 19), // YYYY-MM-DDTHH:mm:ss
      moment.tz(timezone).format('Z') // [+/-]HH:mm]
    ].join('')
    out[key] = new Date(convertedDate)
  })
  return [null, out]
}

module.exports = function (debug) {
  return function timezonedDatesDelegate(logger, timezone, cb) {
    if (!timezone) {
      debug('No timezone provided, model not updated')
      logger.info('No timezone provided, model not updated')
      return
    }
    if (!this.model.get('startDate') || !this.model.get('endDate')) {
      debug('No start or end date provided, model not updated')
      logger.info('No start or end date provided, model not updated')
      return
    }
    if (
      !this.model.get('timezoneCorrectStartDate') ||
      !this.model.get('timezoneCorrectEndDate')
    ) {
      debug(
        'No timezoneCorrectStartDate or timezoneCorrectEndDate provided, continuing...'
      )
      logger.info(
        'No timezoneCorrectStartDate or timezoneCorrectEndDate provided, continuing...'
      )
    }
    if (!logger) {
      debug('No logger provided, model not updated')
      // eslint-disable-next-line
      console.info('No logger provided, model not updated')
      return
    }

    const cbMode = typeof cb === 'function'

    const map = {
      startDate: this.model.get('startDate'),
      endDate: this.model.get('endDate')
    }

    const [err, timezonedDates] = applyTimezone(timezone, map)
    debug('timezonedDates: ', timezonedDates)
    logger.info('timezonedDates: ', timezonedDates)
    if (err) {
      debug('Error applying timezone: ', err)
      logger.error('Error applying timezone: ', err)
      modal({
        title: 'Error',
        content: err.message,
        buttons: [{ text: 'Dismiss', className: 'btn' }]
      })
      return
    }

    this.model.set('timezoneCorrectStartDate', timezonedDates.startDate)
    this.model.set('timezoneCorrectEndDate', timezonedDates.endDate)

    logger.info('timezone conversion complete: ', this.model.toJSON())

    if (cbMode) return cb(null, this.model)
  }
}
