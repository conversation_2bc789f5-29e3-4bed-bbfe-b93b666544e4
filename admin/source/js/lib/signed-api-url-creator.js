const createSignature = require('cf-signature')
const url = require('url')

module.exports = (uri, apiKey, apiId, apiUrl) => {
  const date = Date.now()
  const hash = createSignature(apiKey, 'GET', '', date, uri)
  /* eslint-disable node/no-deprecated-api */
  const urlParts = url.parse(uri, true)

  urlParts.query = {
    ...urlParts.query,
    authorization: apiId + ':' + hash,
    'x-cf-date': date
  }

  urlParts.search = null

  return apiUrl + url.format(urlParts)
}
