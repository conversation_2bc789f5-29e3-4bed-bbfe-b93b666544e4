const extend = require('lodash.assign')
const ckeConfig = require('./ck-editor-config')

function destroy(instance) {
  instance.updateElement()
  $(instance.element).change()
  try {
    instance.destroy()
  } catch (e) {
    if (typeof console !== 'undefined' && console.error)
      console.error('Error destroying CKEditor instance', e)
  }
}

class RichTextEditorInstanceManager {
  constructor() {
    this.ckEditorInstances = {}
  }

  destroy() {
    Object.values(this.ckEditorInstances).forEach(destroy)
    window.CKEDITOR.instances = {}
    this.ckEditorInstances = {}
  }

  destroySingle(name) {
    const instance = window.CKEDITOR.instances[name]
    delete this.ckEditorInstances[instance.name]
    destroy(instance)
  }

  getInstance(name) {
    const instance = window.CKEDITOR.instances[name]
    return instance
  }

  create(el, config) {
    const name =
      el.getAttribute('data-name-alias') ||
      el.name ||
      el.id ||
      '__instance-' + Object.keys(this.ckEditorInstances).length + 1
    if (window.CKEDITOR.instances[name]) return
    const instance = window.CKEDITOR.replace(
      el,
      extend({}, ckeConfig(), config)
    )
    instance.on('change', function () {
      instance.updateElement()
      $(el).change()
    })
    this.ckEditorInstances[name] = instance
  }

  createInline(el, config) {
    const name =
      el.name ||
      el.id ||
      '__instance-' + Object.keys(this.ckEditorInstances).length + 1
    if (window.CKEDITOR.instances[name]) {
      destroy(window.CKEDITOR.instances[name])
      delete this.ckEditorInstances[name]
    }
    const instance = window.CKEDITOR.inline(el, extend({}, ckeConfig(), config))
    instance.on('change', function () {
      $(el).change()
    })
    this.ckEditorInstances[name] = instance
  }
}

module.exports = RichTextEditorInstanceManager
