var interfaceCheck = require('../interface-check')

module.exports = window.Backbone.View.extend({
  events: { 'click .js-select': 'toggleSelect', 'click .js-edit': 'edit' },

  className: 'grid__item one-whole',
  initialize: function () {
    ;['templates'].forEach(interfaceCheck.bind(this, 'object'))
    this.listenTo(this.model, 'select', this.select)
    this.listenTo(this.model, 'deSelect', this.deSelect)
    this.render()
  },

  edit: function () {
    this.trigger('edit')
  },

  toggleSelect: function (e) {
    var isChecked = $(e.target).is(':checked')
    this.model.trigger(isChecked ? 'select' : 'deSelect', this.model)
  },

  select: function () {
    this.$('.js-select')[0].checked = true
  },

  deSelect: function () {
    this.$('.js-select')[0].checked = false
  },

  setLayout: function (layout) {
    if (layout !== this.layout) {
      if (!(layout in this.templates)) throw new Error('Unsupported layout')
      this.layout = layout
      this.render()
    }
    // Special case for thumbnail layout, needs grid class
    if (layout === 'thumbnail') {
      this.$el.removeClass('one-whole').addClass('one-third')
    } else {
      this.$el.addClass('one-whole').removeClass('one-third')
    }
  },

  render: function () {
    this.$el
      .empty()
      .append(this.templates[this.layout]({ data: this.model.toJSON() }))
    return this
  }
})
