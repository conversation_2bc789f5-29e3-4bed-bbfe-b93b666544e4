const uniqBy = require('lodash.uniqby')

/*
 * Sets a default target='_blank' to links
 */
window.CKEDITOR.on('dialogDefinition', function (ev) {
  try {
    const dialogName = ev.data.name
    if (dialogName !== 'link') {
      return
    }

    const dialogDefinition = ev.data.definition
    const informationTab = dialogDefinition.getContents('target')
    const targetField = informationTab.get('linkTargetType')

    targetField.default = '_blank'
  } catch (e) {
    console.error(e)
  }
})

// Allow sharing link anchors with any currently active ckeditor instance
// if you do not correctly destroy instances when removing views then this will
// cause stale instances to be interacted with.
const getEditorAnchors = window.CKEDITOR.plugins.link.getEditorAnchors.bind(
  window.CKEDITOR.plugins.link
)
const getCombinedEditorAnchors = () => {
  const anchors = Object.values(window.CKEDITOR.instances).reduce(
    (anchors, instance) => anchors.concat(getEditorAnchors(instance)),
    []
  )
  return uniqBy(anchors, ({ name, id }) => `${name}:${id}`)
}

window.CKEDITOR.plugins.link.getEditorAnchors = getCombinedEditorAnchors

const getLinkAttributes = window.CKEDITOR.plugins.link.getLinkAttributes.bind(
  window.CKEDITOR.plugins.link
)

// This overrides the final saved attributes of a link from the dialogue bpx
// We need to remove target as it is invalid in this context, and is set from
// the top function
const getReducedLinkAttributes = (instance, data) => {
  const attributes = getLinkAttributes(instance, data)
  if (data.type === 'anchor') {
    delete data.target
    delete attributes.set.target
    attributes.removed.push('target')
  }
  return attributes
}
window.CKEDITOR.plugins.link.getLinkAttributes = getReducedLinkAttributes
