const parseDomain = require('./domain-parser')

module.exports = function (redirect, reason) {
  window.localStorage.removeItem('aclRoles')
  window.localStorage.removeItem('apiId')
  window.localStorage.removeItem('apiKey')
  window.localStorage.removeItem('apiTimeout')
  window.localStorage.removeItem('firstName')

  document.cookie = [
    'cmsLoggedIn=false',
    'path=/',
    `domain=${parseDomain(window.location.hostname)}`
  ].join(';')

  if (redirect) {
    document.location = '/login?reason=' + (reason || '')
  }
}
