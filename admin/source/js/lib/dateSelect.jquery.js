;(function ($) {
  $.fn.dateSelect = function (initialDate) {
    if (initialDate === 'destroy') {
      this.each(function (index, element) {
        $(element).datetimepicker('destroy')
      })

      $('#ui-datepicker-div').remove()

      return this
    }

    return this.datepicker({
      dateFormat: 'dd MM yy',
      showOtherMonths: true,
      changeYear: true,
      changeMonth: true,
      yearRange: 'c-100:c'
    })
      .datepicker('setDate', initialDate)
      .attr('readonly', true)
  }
})(jQuery)
