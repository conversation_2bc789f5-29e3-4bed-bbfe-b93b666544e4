window.jade = require('jade/runtime')

const formErrorsDelegate = require('./lib/form-errors-delegate')()
const apiUrl = $('.js-config').data('api-url')

// Needs to fudge a context
formErrorsDelegate.$ = $

require('./lib/password-match')('.js-setup')

$('.js-setup').on('click', function (e) {
  e.preventDefault()
  var data = {
    emailAddress: $('input[name=emailAddress]').val(),
    password: $('input[name=password]').val(),
    firstName: $('input[name=firstName]').val(),
    lastName: $('input[name=lastName]').val()
  }
  formErrorsDelegate.clearErrors()
  $.ajax({
    type: 'POST',
    url: apiUrl + '/administrator/setup',
    data: JSON.stringify(data),
    success: function () {
      document.location = '/login?reason=setup'
    },
    context: this,
    error: function (error) {
      var parseError = JSON.parse(error.responseText)
      if (parseError.errors) {
        formErrorsDelegate.showErrors(parseError.errors)
      } else {
        formErrorsDelegate.showErrors(parseError)
      }
    },
    dataType: 'json',
    contentType: 'application/json'
  })
  return false
})
