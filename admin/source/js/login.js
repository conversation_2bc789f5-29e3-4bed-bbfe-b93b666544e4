window.jade = require('jade/runtime')
const compileJade = require('browjadify-compile')
const join = require('path').join

const formErrorsDelegate = require('./lib/form-errors-delegate')()
const $email = $('input[name=emailAddress]')
const $password = $('input[name=password]')
const twoFaTemplate = compileJade(
  join(__dirname, '/../../views/templates/two-fa.jade')
)
const twoFaClickHandler = require('./two-fa')
const performLogin = require('./lib/perform-login')
const performLogout = require('./lib/perform-logout')
const FingerprintGenerator = require('fingerprintjs2')
const qs = require('querystring')
const apiUrl = $('.js-config').data('api-url')
const adminUrl = $('.js-config').data('admin-url')

// Needs to fudge a context
formErrorsDelegate.$ = $

$('.js-forgot').on('click', function (e) {
  e.preventDefault()
  document.location =
    '/password-reset-request?emailAddress=' + encodeURIComponent($email.val())
})

$('.js-saml-login').on('click', function (e) {
  e.preventDefault()
  document.location = '/saml/login'
})

// Make sure you aren't logged in
performLogout()

$('form').on('submit', function (e) {
  e.preventDefault()
  $('.js-login').trigger('click')
})

$('.js-login').on('click', function (e) {
  e.preventDefault()
  formErrorsDelegate.clearErrors()

  FingerprintGenerator.get((fingerprintComponents) => {
    const values = fingerprintComponents.map(({ value }) => value)
    const fingerprint = FingerprintGenerator.x64hash128(values.join(''), 31)
    const creds = {
      identity: $email.val(),
      password: $password.val(),
      fingerprint
    }
    let qsData = null

    $.ajax({
      type: 'POST',
      url: apiUrl + '/auth',
      data: JSON.stringify(creds),
      success: function (data) {
        if (data.key) {
          // allow login
          performLogin(data)
        } else if (data.id && !data.forcePasswordReset) {
          // setup 2fa
          $('.js-panel-content').empty()
          if (data.twoFaKey) {
            // split string into 4 character chunks
            data.twoFaKey = data.twoFaKey
              .replace(/[^\dA-Z]/g, '')
              .replace(/(.{4})/g, '$1 ')
              .trim()
          }
          var twoFaContent = twoFaTemplate(data)
          $('.js-panel-content').append(twoFaContent)
          $('.js-2fa-submit').click(function (e) {
            e.preventDefault()
            data.fingerprint = fingerprint
            twoFaClickHandler(data)
          })
        } else if (data.forcePasswordReset) {
          // force password reset
          qsData = {
            emailAddress: data.emailAddress,
            token: data.resetToken.token,
            expiry: data.resetToken.expiry
          }
          window.location.replace('/password-reset?' + qs.stringify(qsData))
        } else {
          // we had errors
          $('.js-errors-summary').html(data)
        }
      },
      context: this,
      error: function (error) {
        var parseError = JSON.parse(error.responseText)
        if (parseError.errors) {
          formErrorsDelegate.showErrors(parseError.errors)
        } else {
          formErrorsDelegate.showErrors(parseError)
        }
      },
      dataType: 'json',
      contentType: 'application/json'
    })
  })

  return false
})

$('.js-request').on('click', function (e) {
  e.preventDefault()
  var $email = $('input[name=emailAddress]')
  formErrorsDelegate.clearErrors()
  $.ajax({
    type: 'POST',
    url: apiUrl + '/administrator/password-reset-request',
    data: JSON.stringify({
      emailAddress: $email.val(),
      returnUrl: adminUrl + '/password-reset'
    }),
    success: function () {
      $('.panel-content').html('<p>Instructions have been sent to you.</p>')
    },
    context: this,
    error: function (error) {
      var parseError = JSON.parse(error.responseText)
      if (parseError.errors) {
        formErrorsDelegate.showErrors(parseError.errors)
      } else {
        formErrorsDelegate.showErrors(parseError)
      }
    },
    dataType: 'json',
    contentType: 'application/json'
  })
  return false
})

require('./lib/password-match')('.js-reset')

$('.js-reset').on('click', function (e) {
  e.preventDefault()
  var $password = $('input[name=password]')
  formErrorsDelegate.clearErrors()
  $.ajax({
    type: 'POST',
    url: apiUrl + '/administrator/password-reset',
    data: JSON.stringify({
      emailAddress: $('input[name=emailAddress]').val(),
      token: $('input[name=token]').val(),
      expiry: $('input[name=expiry]').val(),
      password: $password.val()
    }),

    success: function () {
      document.location = '/login?reason=reset'
    },
    context: this,
    error: function (error) {
      var parseError = JSON.parse(error.responseText)
      if (parseError.errors) {
        formErrorsDelegate.showErrors(parseError.errors)
      } else {
        formErrorsDelegate.showErrors(parseError)
      }
    },
    dataType: 'json',
    contentType: 'application/json'
  })
  return false
})
