module.exports = tasks
const join = require('path').join
const glob = require('glob')
const createConfigury = require('@clocklimited/configury')
const configury = createConfigury(join(__dirname, '/../config.json'))
const config = configury(process.NODE_ENV)

function tasks(pliers) {
  // Load pliers plugins
  glob.sync(join(__dirname, '/pliers/*.js')).forEach(function (file) {
    require(file)(pliers, config)
  })

  // Load filesets
  glob.sync(join(__dirname, '/pliers/filesets/*.js')).forEach(function (file) {
    require(file)(pliers, config)
  })
}
