//-
//- STANDARD META TAGS
//-

meta(charset='utf-8')
meta(name='viewport', content='initial-scale=1')


//-
//- PAGE TITLE
//-

block title
  title #{config.title} CMS


//-
//- CSS
//-

link(rel='stylesheet', href=versionPath('/assets/css/index.css'))
link(rel='stylesheet', href='//fonts.googleapis.com/css?family=Open+Sans:300,400,600')


//-
//- JAVASCRIPT
//-

block head-scripts
  script
    include ../../../assets/js/build/modernizr.js

//-
//- ICONS / IMAGES / DEVICE SPECIFICS
//-

if environment === 'production'
  - var faviconPath = 'favicon.ico'
else if environment === 'development' || environment === 'testing' || environment === 'staging'
  - var faviconPath = 'favicon-' + environment + '.ico'
else
  - var faviconPath = 'favicon-unknown.ico'

link(rel='shortcut icon', href=versionPath('/assets/img/meta/#{faviconPath}'))
