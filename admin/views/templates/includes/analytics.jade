//- GA guidelines state this code must follow the opening body tag
if (config.adminGaPropertyId)
  script.
    (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
      (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
      m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
      })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

      ga('create', '#{config.adminGaPropertyId}', 'auto')
      ga('set', '&uid', window.localStorage.getItem('apiId'))
      ga('send', 'pageview')
