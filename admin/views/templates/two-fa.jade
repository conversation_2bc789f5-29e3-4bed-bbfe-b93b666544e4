.panel-header
  h2 Two Step Verification Code
  if message
    p= message

.panel-content

  if twoFaKey
    h3 Setup Google Authenticator

    p Install the Google Authenticator app on your Phone
    ol
      li On your phone, open up the App Store
      li Search for "Google Authenticator"
      li Download and install the application

    p Open and Configure Google Authenticator
    ol
      li In Google Authenticator, setup a new account
      li Choose "Scan barcode"
      li Use your phones camera to scan this QR code

    .text-centering 
      img.p(src=twoFaQrUrl)

    p Can't scan the barcode above?
    ol
      li In Google Authenticator, setup a new account
      li Choose "Manual Entry"
      li Follow the on screen instructions, entering the key below when prompted

    pre.text-centering
      span=twoFaKey

    p Please enter the six digit verification code generated by Google Authenticator below

  form(method='post')
    .js-errors-summary

    .form-row(data-field='code')
      label
        span.form-label-text Code
        input.control.control--text.form-field(type='text', name='code')
      .js-error

    .form-row(data-field='rememberTwoFa')
      label
        input.control.control--boolean(type='checkbox', name='rememberTwoFa', checked='checked')
        span Remember this device for 30 days?

    .form-row.form-row-actions
      .form-field
        button.btn.btn--action.js-2fa-submit(type='submit') Submit
