extend layout

block content

  .page-content.js-config(data-api-url=config.apiUrl)
    .centering.centering-thin

      h1= config.title

      .panel.panel-styled.js-panel-content

        .panel-header
          h2 Sign In
          if message
            p= message

        .panel-content
          form(method='post')
            .js-errors-summary

            .form-row.form-row-full-width(data-field='email')
              label
                span.form-label-text Email Address
                input.control.control--text.form-field(type='email', name='emailAddress', autofocus)
              .js-error

            .form-row.form-row-full-width(data-field='password')
              label
                span.form-label-text Password
                input.control.control--text.form-field(type='password', name='password')
              .js-error

            .form-row.form-row-full-width
              a.btn.text-btn.btn-saml.js-saml-login
                svg.icon(xmlns="http://www.w3.org/2000/svg" width="20" viewBox="0 0 23 23")
                  path(fill="#f35325" d="M1 1h10v10H1z")
                  path(fill="#81bc06" d="M12 1h10v10H12z")
                  path(fill="#05a6f0" d="M1 12h10v10H1z")
                  path(fill="#ffba08" d="M12 12h10v10H12z")
                span Login with Microsoft

            .form-row.form-row-actions.form-row-full-width
              .form-field
                .form-row-actions-left
                  a.js-forgot(href='#') Forgotten your password?
                button.btn.btn--action.js-login(type='submit') Sign In

append script
  script(src=versionPath('/assets/js/build/login.js'))
