extend layout

block content

  .page-content.js-config(data-api-url=config.apiUrl)
    .centering.centering-thin

      h1= config.title

      .panel.panel-styled

        .panel-header
          h2 Create Administrator Account

        .panel-content
          p Please create an 
            strong Administrator who will have ultimate privileges
            | . You should then create further administrator accounts with restricted access for general usage.

          form(method='post')
            .js-errors-summary

            .form-row(data-field='emailAddress')
              label
                span.form-label-text Email
                input.control.control--text.form-field(type='email', name='emailAddress', autofocus)
              .js-error

            .form-row(data-field='firstName')
              label
                span.form-label-text First Name
                input.control.control--text.form-field(type='text', name='firstName')
              .js-error

            .form-row(data-field='lastName')
              label
                span.form-label-text Last Name
                input.control.control--text.form-field(type='text', name='lastName')
              .js-error

            .form-row(data-field='password')
              label
                span.form-label-text Password
                input.control.control--text.form-field(type='password', name='password')
              .js-error

            .form-row(data-field='confirmPassword')
              label
                span.form-label-text Confirm Password
                input.control.control--text.form-field(type='password', name='confirmPassword')
              .js-error

            .form-row.form-row-actions
              button.btn.btn--action.js-setup(type='submit') Setup

append script
  script(src=versionPath('/assets/js/build/setup.js'))
