extend layout

block content
  .page-content.js-reset-form.js-config(data-api-url=config.apiUrl)
    .centering.centering-thin

      h1= config.title

      .panel.panel-styled

        .panel-header
          h2 Password Reset
          p Enter your new password.
          p It must not be the same as your previous 3 passwords.

        .panel-content
          form(method='post')
            .js-errors-summary

            .form-row(data-field='password')
              label
                span.form-label-text Password
                input.control.control--text.form-field(type='password', name='password')
              .js-error

            .form-row(data-field='confirmPassword')
              label
                span.form-label-text Confirm Password
                input.control.control--text.form-field(type='password', name='confirmPassword')
              .js-error

            input(type='hidden', name='emailAddress', value=emailAddress)
            input(type='hidden', name='token', value=token)
            input(type='hidden', name='expiry', value=expiry)

            .form-row.form-row-actions
              button.btn.btn--action.js-reset(type='submit') Submit

append script
  script(src=versionPath('/assets/js/build/login.js'))
